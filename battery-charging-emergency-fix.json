[{"id": "7aa6bd9d6c51a86b", "type": "tab", "label": "電池充電緊急修正版", "disabled": false, "info": "緊急修正版本 - 解決過度充電問題\n\n🚨 緊急功能：\n1. 立即關閉所有充電設備\n2. 檢查當前充電狀態\n3. 修正NotifyHelper通知\n\n⚠️ 使用說明：\n1. 先點擊「緊急關閉所有充電」\n2. 檢查設備和電池狀態\n3. 測試修正後的通知功能", "env": []}, {"id": "emergency_stop_all", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "🚨 緊急關閉所有充電", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "emergency_stop", "payloadType": "str", "x": 130, "y": 60, "wires": [["emergency_handler"]]}, {"id": "emergency_handler", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "緊急處理器", "func": "// 電池配置\nconst batteries = [\n    {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_4',\n        name: '牧田BL1041B充電電池'\n    },\n    {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_5',\n        name: 'ENELOOP 3號充電電池'\n    },\n    {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_6',\n        name: 'ENELOOP 4號充電電池'\n    }\n];\n\n// 準備關閉命令\nconst outputs = [];\n\n// 通知訊息\nconst notifyMsg = {\n    payload_notifyhelper: {\n        message: `🚨 緊急關閉所有充電設備\\n\\n已關閉：\\n• 牧田BL1041B充電電池\\n• ENELOOP 3號充電電池\\n• ENELOOP 4號充電電池\\n\\n⏰ 執行時間：${new Date().toLocaleString('zh-TW')}\\n\\n請檢查設備狀態和電池溫度。`,\n        title: '🚨 緊急關閉充電',\n        targets: ['person.ming']\n    }\n};\n\noutputs.push(notifyMsg);\n\n// 為每個電池創建關閉命令\nbatteries.forEach(battery => {\n    outputs.push({\n        payload: {\n            domain: 'switch',\n            service: 'turn_off',\n            target: {\n                entity_id: battery.entityId\n            }\n        },\n        batteryName: battery.name\n    });\n});\n\nnode.status({fill:\"red\", shape:\"dot\", text:\"緊急關閉執行\"});\n\nreturn outputs;", "outputs": 4, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 60, "wires": [["notify_helper"], ["device_control"], ["device_control"], ["device_control"]]}, {"id": "check_status", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "🔍 檢查充電狀態", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "check_status", "payloadType": "str", "x": 130, "y": 120, "wires": [["status_checker"]]}, {"id": "status_checker", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "狀態檢查器", "func": "// 準備狀態檢查通知\nconst notifyMsg = {\n    payload_notifyhelper: {\n        message: `🔍 充電狀態檢查\\n\\n請手動檢查以下設備：\\n\\n🔌 插座4 (牧田BL1041B)\\n🔌 插座5 (ENELOOP 3號)\\n🔌 插座6 (ENELOOP 4號)\\n\\n檢查項目：\\n• 充電器是否還在運作\\n• 電池溫度是否正常\\n• 充電指示燈狀態\\n\\n⏰ 檢查時間：${new Date().toLocaleString('zh-TW')}`,\n        title: '🔍 充電狀態檢查',\n        targets: ['person.ming']\n    }\n};\n\nnode.status({fill:\"blue\", shape:\"dot\", text:\"狀態檢查\"});\n\nreturn notifyMsg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 120, "wires": [["notify_helper"]]}, {"id": "test_notify", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "🧪 測試通知", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "test_notify", "payloadType": "str", "x": 130, "y": 180, "wires": [["test_notifier"]]}, {"id": "test_notifier", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "測試通知器", "func": "// 準備測試通知\nconst notifyMsg = {\n    payload_notifyhelper: {\n        message: `🧪 NotifyHelper測試通知\\n\\n這是一個測試訊息，用於驗證NotifyHelper是否正常工作。\\n\\n✅ 如果您收到這個通知，表示NotifyHelper配置正確。\\n\\n⏰ 測試時間：${new Date().toLocaleString('zh-TW')}`,\n        title: '🧪 NotifyHelper測試',\n        targets: ['person.ming']\n    }\n};\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"測試通知\"});\n\nreturn notifyMsg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 180, "wires": [["notify_helper"]]}, {"id": "notify_helper", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "NotifyHelper (ming)", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.notify_person", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload_notifyhelper.message, \"title\": payload_notifyhelper.title, \"targets\": payload_notifyhelper.targets}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": true, "domain": "notify", "service": "notify_person", "x": 580, "y": 140, "wires": [["debug_notify"]]}, {"id": "device_control", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "設備控制", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "{{payload.domain}}.{{payload.service}}", "floorId": [], "areaId": [], "deviceId": [], "entityId": ["{{payload.target.entity_id}}"], "labelId": [], "data": "{}", "dataType": "json", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "", "service": "", "x": 580, "y": 60, "wires": [["debug_control"]]}, {"id": "debug_notify", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "NotifyHelper Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 800, "y": 140, "wires": []}, {"id": "debug_control", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "控制Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 780, "y": 60, "wires": []}, {"id": "125d83f7.1a33dc", "type": "server", "name": "Home Assistant", "addon": true}]