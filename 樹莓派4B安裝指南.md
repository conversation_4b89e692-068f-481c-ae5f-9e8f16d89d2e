# 🍓 樹莓派4B - 智能充電電池管理系統安裝指南

## 📋 系統環境確認

### 🔍 檢查您的樹莓派4B環境

在開始安裝前，請確認您的環境：

```bash
# 檢查系統版本
cat /etc/os-release

# 檢查Home Assistant狀態
sudo systemctl status home-assistant@homeassistant
# 或者 (如果使用Home Assistant OS)
ha core info

# 檢查Node-RED狀態
sudo systemctl status nodered
# 或者
ps aux | grep node-red
```

### 📁 常見的Home Assistant配置目錄

根據您的安裝方式，配置目錄可能在：

| 安裝方式 | 配置目錄路徑 |
|---------|-------------|
| Home Assistant OS | `/config` |
| Home Assistant Supervised | `/usr/share/hassio/homeassistant` |
| Home Assistant Core (venv) | `/home/<USER>/.homeassistant` |
| Home Assistant Core (Docker) | `/opt/homeassistant/.homeassistant` |
| 手動安裝 | `~/.homeassistant` |

---

## 🚀 步驟1：更新Node-RED Home Assistant節點

### 檢查當前版本

```bash
# 進入Node-RED目錄
cd ~/.node-red

# 檢查已安裝的Home Assistant節點版本
npm list node-red-contrib-home-assistant-websocket
```

### 更新到最新版本

```bash
# 更新Home Assistant節點 (解決call-service問題)
npm update node-red-contrib-home-assistant-websocket

# 或者重新安裝
npm uninstall node-red-contrib-home-assistant-websocket
npm install node-red-contrib-home-assistant-websocket@latest

# 重啟Node-RED
sudo systemctl restart nodered
```

### 🔧 如果遇到權限問題

```bash
# 修改Node-RED目錄權限
sudo chown -R pi:pi ~/.node-red

# 或者使用sudo安裝 (不推薦，但有時必要)
sudo npm install -g --unsafe-perm node-red-contrib-home-assistant-websocket
```

---

## 🚀 步驟2：安裝Home Assistant輔助實體

### 方法一：自動檢測配置目錄

```bash
# 使用我們的安裝腳本
chmod +x install-battery-management.sh
./install-battery-management.sh
```

### 方法二：手動安裝

1. **找到您的配置目錄**
   ```bash
   # 檢查可能的配置目錄
   ls -la /config 2>/dev/null && echo "找到 /config"
   ls -la /usr/share/hassio/homeassistant 2>/dev/null && echo "找到 Supervised 目錄"
   ls -la ~/.homeassistant 2>/dev/null && echo "找到用戶目錄"
   ```

2. **創建packages目錄**
   ```bash
   # 替換 CONFIG_DIR 為您的實際配置目錄
   CONFIG_DIR="/config"  # 或您找到的實際路徑
   
   mkdir -p $CONFIG_DIR/packages
   ```

3. **複製配置文件**
   ```bash
   # 複製輔助實體配置
   cp battery-helpers.yaml $CONFIG_DIR/packages/
   ```

4. **修改configuration.yaml**
   ```bash
   # 檢查是否已有packages配置
   grep -q "packages:" $CONFIG_DIR/configuration.yaml
   
   # 如果沒有，添加packages配置
   if [ $? -ne 0 ]; then
       echo "" >> $CONFIG_DIR/configuration.yaml
       echo "# 智能充電電池管理系統" >> $CONFIG_DIR/configuration.yaml
       echo "homeassistant:" >> $CONFIG_DIR/configuration.yaml
       echo "  packages: !include_dir_named packages/" >> $CONFIG_DIR/configuration.yaml
   fi
   ```

5. **重啟Home Assistant**
   ```bash
   # 根據您的安裝方式選擇
   sudo systemctl restart home-assistant@homeassistant
   # 或
   ha core restart
   ```

---

## 🚀 步驟3：導入Node-RED流程

### 1. 打開Node-RED編輯器

```
http://樹莓派IP:1880
```

### 2. 導入修正後的流程

1. 點擊右上角選單 → 導入
2. 選擇更新後的 `battery-charging-flow.json` 文件
3. 點擊「導入」

**重要**：新版本使用 `api-call-service` 節點類型，已修正相容性問題。

### 3. 配置Home Assistant連接

1. 雙擊任一個監控節點
2. 點擊「Server」旁的編輯按鈕
3. 配置連接資訊：
   - **Name**: `Home Assistant`
   - **Base URL**: `http://localhost:8123` (同台樹莓派)
   - **Access Token**: 從HA用戶設定中生成長期存取權杖

### 4. 載入配置文件

1. **複製配置文件到Node-RED目錄**
   ```bash
   cp battery-config.js ~/.node-red/
   ```

2. **添加配置載入節點**
   
   在Node-RED中添加：
   - **Inject節點** (設定為啟動時執行一次)
   - **Function節點** (載入配置)
   
   Function節點代碼：
   ```javascript
   // 載入電池充電配置
   try {
       const config = require('./battery-config.js');
       global.set('batteryChargingConfig', config);
       node.status({fill:"green", shape:"dot", text:"配置已載入"});
       msg.payload = "電池充電配置已成功載入";
   } catch (error) {
       node.status({fill:"red", shape:"ring", text:"配置載入失敗"});
       msg.payload = "配置載入失敗: " + error.message;
       node.error(error);
   }
   return msg;
   ```

3. **部署並測試**
   - 點擊「Deploy」
   - 點擊inject節點測試配置載入
   - 檢查debug輸出確認配置成功

---

## 🔧 樹莓派4B特殊設定

### 📱 通知服務配置

編輯 `battery-config.js` 中的通知設定，根據您的樹莓派環境調整：

```javascript
const notificationConfig = {
    notifyHelper: {
        enabled: true,
        service: 'notify.notify',  // 或 'notify.mobile_app_your_phone'
        targets: ['person.ming'], // 改為您的person實體
        priority: 'normal'
    },
    synologyChat: {
        enabled: true,
        service: 'notify.synology_chat_bot_3', // 您的實際服務名稱
        username: 'Battery Manager',
        channel: '#smart-home'
    },
    telegram: {
        enabled: true,
        service: 'notify.telegram', // 您的實際服務名稱
        parseMode: 'HTML'
    }
};
```

### 🕐 時區設定

確保樹莓派時區正確：

```bash
# 檢查當前時區
timedatectl

# 設定台北時區
sudo timedatectl set-timezone Asia/Taipei

# 同步時間
sudo systemctl restart systemd-timesyncd
```

### 💾 SD卡優化 (可選)

為了減少SD卡寫入，可以調整日誌設定：

```bash
# 編輯Node-RED設定
nano ~/.node-red/settings.js

# 在settings.js中添加或修改：
logging: {
    console: {
        level: "info",
        metrics: false,
        audit: false
    }
}
```

---

## ✅ 驗證安裝

### 1. 檢查Home Assistant實體

在Home Assistant中確認新增的實體：

```
設定 → 裝置與服務 → 實體
```

搜尋 `battery` 應該看到：
- `input_boolean.battery_management_enabled`
- `input_number.makita_charging_time`
- `sensor.makita_days_since_charge`
- 等等...

### 2. 測試Node-RED流程

1. 在Node-RED中檢查所有節點都是綠色連接狀態
2. 手動觸發inject節點測試配置載入
3. 檢查debug輸出確認無錯誤

### 3. 測試充電功能

1. **手動測試**：
   - 手動開啟任一充電插座
   - 檢查是否收到充電開始通知
   - 等待幾分鐘後手動關閉
   - 檢查是否收到充電完成通知

2. **檢查狀態**：
   - 在Home Assistant中查看電池狀態感測器
   - 確認上次充電時間已更新

---

## 🛠️ 故障排除

### ❌ Node-RED節點顯示unknown

**原因**：Node-RED Home Assistant節點版本過舊

**解決方案**：
```bash
# 更新節點
cd ~/.node-red
npm update node-red-contrib-home-assistant-websocket
sudo systemctl restart nodered
```

### ❌ Home Assistant連接失敗

**檢查項目**：
1. Home Assistant是否正在運行
2. 存取權杖是否正確
3. 網路連接是否正常

**解決方案**：
```bash
# 檢查Home Assistant狀態
sudo systemctl status home-assistant@homeassistant

# 檢查網路連接
curl http://localhost:8123

# 重新生成存取權杖
# 在HA中：用戶設定 → 安全性 → 長期存取權杖
```

### ❌ 配置文件載入失敗

**檢查項目**：
1. 文件路徑是否正確
2. 文件權限是否正確
3. JavaScript語法是否正確

**解決方案**：
```bash
# 檢查文件權限
ls -la ~/.node-red/battery-config.js

# 修正權限
chmod 644 ~/.node-red/battery-config.js

# 檢查語法
node -c ~/.node-red/battery-config.js
```

### ❌ 通知未發送

**檢查項目**：
1. 通知服務是否正確配置
2. 是否在靜音時間內
3. 實體ID是否正確

**解決方案**：
1. 在Home Assistant中測試通知服務
2. 檢查Node-RED debug輸出
3. 確認通知服務名稱正確

---

## 📞 樹莓派4B專用支援

### 🔍 系統資訊收集

如果需要技術支援，請提供以下資訊：

```bash
# 系統資訊
uname -a
cat /etc/os-release

# Home Assistant版本
ha core info
# 或
grep "version" /config/.HA_VERSION

# Node-RED版本
node-red --version

# 已安裝的HA節點版本
cd ~/.node-red && npm list node-red-contrib-home-assistant-websocket
```

### 📊 效能監控

```bash
# 檢查系統資源使用
htop

# 檢查SD卡使用情況
df -h

# 檢查記憶體使用
free -h
```

---

## 🎉 完成！

您的樹莓派4B智能充電電池管理系統現在已經準備就緒！

**下一步**：
1. 測試所有充電插座功能
2. 調整充電時間參數
3. 設定個人化通知
4. 享受智能充電管理！

如有任何問題，請參考故障排除章節或檢查系統日誌。
