# 🔋 智能充電電池管理系統 - 簡化版使用說明

## 🎯 解決的問題

✅ **解決JSON錯誤** - 移除了有問題的HA記錄更新節點  
✅ **解決ACTION空白** - 簡化了api-call-service節點配置  
✅ **解決通知問題** - 重新設計通知邏輯  
✅ **添加Debug節點** - 每個關鍵步驟都有debug輸出  
✅ **無需yaml文件** - 完全使用Node-RED實現，不需要修改configuration.yaml  

---

## 🚀 5分鐘快速安裝

### 步驟1：導入簡化版流程

1. **打開Node-RED編輯器**
   ```
   http://樹莓派IP:1880
   ```

2. **導入流程**
   - 點擊右上角選單 → 導入
   - 選擇 `battery-charging-simple.json` 文件
   - 點擊「導入」

3. **配置Home Assistant連接**
   - 雙擊任一個監控節點
   - 點擊「Server」旁的編輯按鈕
   - 填入：
     - **Name**: `Home Assistant`
     - **Base URL**: `http://localhost:8123`
     - **Access Token**: 從HA用戶設定中生成

4. **部署流程**
   - 點擊右上角「Deploy」按鈕
   - 等待部署完成

### 步驟2：初始化配置

1. **執行初始化**
   - 找到「初始化配置」inject節點
   - 點擊左側的按鈕執行
   - 檢查debug輸出確認配置載入成功

2. **檢查配置**
   - 在debug面板中應該看到配置已載入的訊息
   - 確認所有電池配置都正確

---

## 🎮 使用方法

### 🔋 手動充電測試

1. **開始充電**
   - 手動開啟任一充電插座（插座4、5、6）
   - 檢查debug輸出，應該看到充電開始訊息
   - 檢查是否收到通知

2. **自動關閉**
   - 系統會在設定時間後自動關閉插座
   - 檢查debug輸出確認自動關閉
   - 檢查是否收到充電完成通知

### 🔄 維護充電

1. **自動維護檢查**
   - 系統每日早上8點自動檢查
   - 或點擊「手動維護檢查」按鈕

2. **查看充電記錄**
   - 點擊「查看充電記錄」按鈕
   - 在debug輸出中查看詳細報告

---

## 🔧 配置說明

### 📱 通知服務設定

在「配置設定」function節點中修改通知服務：

```javascript
// 通知配置
const notifyConfig = {
    services: [
        'notify.notify',                    // 改為您的實際服務
        'notify.synology_chat_bot_3',      // 改為您的實際服務
        'notify.telegram'                   // 改為您的實際服務
    ],
    enabled: true
};
```

### ⏰ 充電時間調整

在「配置設定」function節點中修改充電時間：

```javascript
// 電池配置
const batteryConfig = {
    makita: {
        entityId: 'media_player.volume_set',
        name: '牧田BL1041B充電電池',
        chargingTime: 90,        // 充電時間（分鐘）
        maintenanceDays: 30,     // 維護週期（天）
        maxTime: 180             // 最大充電時間（分鐘）
    },
    eneloop_aa: {
        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_5',
        name: 'ENELOOP 3號充電電池',
        chargingTime: 240,       // 4小時
        maintenanceDays: 60,     // 60天維護週期
        maxTime: 360
    },
    eneloop_aaa: {
        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_6',
        name: 'ENELOOP 4號充電電池',
        chargingTime: 210,       // 3.5小時
        maintenanceDays: 60,     // 60天維護週期
        maxTime: 300
    }
};
```

---

## 🐛 Debug功能

### 📊 Debug節點說明

系統包含多個debug節點，幫助您調試問題：

1. **配置Debug** - 顯示系統配置載入狀態
2. **電池Debug** - 顯示電池狀態變化和充電邏輯
3. **通知Debug** - 顯示通知發送狀態
4. **控制Debug** - 顯示設備控制命令
5. **維護Debug** - 顯示維護充電邏輯
6. **記錄Debug** - 顯示充電記錄報告

### 🔍 查看Debug輸出

1. **打開Debug面板**
   - 在Node-RED編輯器右側點擊「debug」標籤
   - 或按快捷鍵 `Ctrl+Shift+D`

2. **過濾Debug訊息**
   - 點擊debug節點名稱可以過濾特定類型的訊息
   - 使用搜尋功能查找特定內容

---

## 🛠️ 故障排除

### ❌ 沒有收到通知

**檢查步驟**：
1. 查看「通知Debug」輸出
2. 確認通知服務名稱正確
3. 檢查是否在靜音時間（22:00-07:00）

**解決方案**：
```javascript
// 在配置設定中修改通知服務名稱
const notifyConfig = {
    services: [
        'notify.notify',  // 確認這是您的實際服務名稱
    ],
    enabled: true
};
```

### ❌ 插座沒有自動關閉

**檢查步驟**：
1. 查看「電池Debug」輸出
2. 確認計時器是否正確設定
3. 檢查「控制Debug」是否有自動關閉命令

**解決方案**：
1. 確認實體ID正確
2. 檢查Home Assistant連接
3. 重新部署流程

### ❌ 配置載入失敗

**檢查步驟**：
1. 查看「配置Debug」輸出
2. 確認初始化inject節點已執行

**解決方案**：
1. 點擊「初始化配置」inject節點
2. 檢查function節點中的JavaScript語法
3. 重新部署流程

### ❌ 維護充電沒有觸發

**檢查步驟**：
1. 點擊「查看充電記錄」檢查記錄
2. 點擊「手動維護檢查」測試
3. 查看「維護Debug」輸出

**解決方案**：
1. 確認有充電記錄
2. 檢查維護週期設定
3. 手動觸發維護檢查

---

## 📊 功能測試清單

### ✅ 基本功能測試

- [ ] 配置初始化成功
- [ ] 手動開啟插座4（牧田）有通知
- [ ] 手動開啟插座5（ENELOOP 3號）有通知  
- [ ] 手動開啟插座6（ENELOOP 4號）有通知
- [ ] 自動關閉功能正常
- [ ] 充電完成通知正常

### ✅ 進階功能測試

- [ ] 維護檢查功能正常
- [ ] 充電記錄查看正常
- [ ] Debug輸出完整
- [ ] 靜音時間功能正常

---

## 🎉 優勢特色

### 🚀 簡化版優勢

1. **無需yaml文件** - 不用修改Home Assistant配置
2. **完整Debug** - 每個步驟都有詳細輸出
3. **易於調試** - 問題一目了然
4. **配置簡單** - 所有設定都在Node-RED中
5. **功能完整** - 包含所有原有功能

### 🔧 技術特色

- ✅ 使用Node-RED context storage存儲數據
- ✅ 簡化的api-call-service節點配置
- ✅ 完整的錯誤處理和debug輸出
- ✅ 模組化的設計，易於維護
- ✅ 無外部依賴，純Node-RED實現

---

## 📞 技術支援

### 🔍 收集Debug資訊

如果遇到問題，請提供以下debug輸出：

1. **配置Debug** - 確認配置載入狀態
2. **電池Debug** - 充電邏輯執行情況
3. **通知Debug** - 通知發送狀態
4. **控制Debug** - 設備控制命令

### 📋 常用檢查命令

```bash
# 檢查Node-RED狀態
sudo systemctl status nodered

# 檢查Home Assistant狀態
sudo systemctl status home-assistant@homeassistant

# 查看Node-RED日誌
journalctl -u nodered -f
```

---

## 🎊 開始享受智能充電！

現在您的簡化版智能充電電池管理系統已經準備就緒！

- ✅ 無需複雜配置
- ✅ 完整的debug功能
- ✅ 易於故障排除
- ✅ 功能完整可靠

**立即開始測試您的智能充電系統吧！** 🔋⚡🏠
