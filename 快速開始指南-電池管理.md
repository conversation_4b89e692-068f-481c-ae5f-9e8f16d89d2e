# 🚀 智能充電電池管理系統 - 快速開始指南

## 📋 系統概述

這個系統專為您的HS300智能插座設計，可以智能管理牧田BL1041B和ENELOOP充電電池的充電過程。

### 🎯 核心功能
- 🔋 **手動充電自動管理**：手動開啟後自動計時關閉
- 🔄 **定期維護充電**：根據電池特性自動維護
- 📱 **多重通知系統**：NotifyHelper、Synology Chat、Telegram
- 🛡️ **安全保護機制**：防止過度充電

### 🔌 您的設備配置

| 電池類型 | 插座位置 | 實體ID | 充電時間 | 維護週期 |
|---------|---------|--------|---------|---------|
| 牧田BL1041B | HS300 2-4插座 | `media_player.volume_set` | 90分鐘 | 30天 |
| ENELOOP 3號 | HS300 2-5插座 | `switch.tp_link_power_strip_eb2f_cha_shang_5` | 240分鐘 | 60天 |
| ENELOOP 4號 | HS300 2-6插座 | `switch.tp_link_power_strip_eb2f_cha_shang_6` | 210分鐘 | 60天 |

---

## ⚡ 5分鐘快速安裝

### 🔧 重要：先修正Node-RED相容性問題

**如果您遇到 `unknown:call-service` 錯誤，請先執行：**

```bash
# 執行相容性修正腳本
chmod +x fix-node-red-compatibility.sh
./fix-node-red-compatibility.sh
```

**或手動修正：**

```bash
# 更新Home Assistant節點
cd ~/.node-red
npm update node-red-contrib-home-assistant-websocket
sudo systemctl restart nodered
```

### 步驟1：安裝Home Assistant輔助實體

1. **複製配置文件**
   ```bash
   # 在您的樹莓派上執行
   cd /config
   mkdir -p packages
   # 將 battery-helpers.yaml 複製到 packages 目錄
   ```

2. **修改configuration.yaml**
   在 `/config/configuration.yaml` 末尾添加：
   ```yaml
   # 智能充電電池管理系統
   homeassistant:
     packages: !include_dir_named packages/
   ```

3. **重啟Home Assistant**
   ```bash
   sudo systemctl restart home-assistant@homeassistant
   ```

### 步驟2：導入Node-RED流程

1. **打開Node-RED編輯器**
   ```
   http://您的樹莓派IP:1880
   ```

2. **導入流程**
   - 點擊右上角選單 → 導入
   - 選擇 `battery-charging-flow.json` 文件
   - 點擊「導入」

3. **配置Home Assistant連接**
   - 雙擊任一個節點
   - 點擊「Server」旁的編輯按鈕
   - 填入：
     - **Base URL**: `http://localhost:8123`
     - **Access Token**: 從HA用戶設定中生成

### 步驟3：載入配置

1. **上傳配置文件**
   將 `battery-config.js` 複製到 Node-RED 目錄：
   ```bash
   cp battery-config.js ~/.node-red/
   ```

2. **添加配置載入節點**
   在Node-RED中添加以下節點：
   
   **Inject節點** → **Function節點**
   
   Function節點代碼：
   ```javascript
   // 載入電池充電配置
   const config = require('./battery-config.js');
   global.set('batteryChargingConfig', config);
   msg.payload = "配置已載入";
   return msg;
   ```

3. **部署並測試**
   - 點擊「Deploy」
   - 點擊inject節點測試配置載入

---

## 🎮 立即開始使用

### 🔋 測試手動充電

1. **開啟插座**：手動開啟任一充電插座
2. **檢查通知**：應該收到充電開始通知
3. **等待自動關閉**：系統會在設定時間後自動關閉
4. **確認完成通知**：收到充電完成通知

### 📱 檢查Home Assistant

在Home Assistant中查看新增的實體：

#### 控制面板
- `input_boolean.battery_management_enabled` - 系統總開關
- `input_boolean.battery_auto_maintenance` - 自動維護開關
- `input_boolean.battery_charging_notifications` - 通知開關

#### 狀態監控
- `sensor.makita_days_since_charge` - 牧田電池距離上次充電天數
- `sensor.eneloop_aa_days_since_charge` - ENELOOP 3號距離上次充電天數
- `sensor.eneloop_aaa_days_since_charge` - ENELOOP 4號距離上次充電天數

#### 時間設定
- `input_number.makita_charging_time` - 牧田充電時間
- `input_number.eneloop_aa_charging_time` - ENELOOP 3號充電時間
- `input_number.eneloop_aaa_charging_time` - ENELOOP 4號充電時間

---

## 🔧 個人化設定

### 📱 通知服務配置

編輯 `battery-config.js` 中的通知設定：

```javascript
const notificationConfig = {
    notifyHelper: {
        enabled: true,
        service: 'notify.notify',
        targets: ['person.ming'], // 改為您的person實體
        priority: 'normal'
    },
    synologyChat: {
        enabled: true,
        service: 'notify.synology_chat_bot_3', // 您的服務名稱
        username: 'Battery Manager',
        channel: '#smart-home'
    },
    telegram: {
        enabled: true,
        service: 'notify.telegram', // 您的服務名稱
        parseMode: 'HTML'
    }
};
```

### ⏰ 充電時間調整

根據您的需求調整充電時間：

```javascript
const batteryConfig = {
    makita: {
        chargingTime: 90,        // 牧田：90分鐘（可調整30-180）
        maintenanceCycle: 30     // 維護週期：30天
    },
    eneloop_aa: {
        chargingTime: 240,       // ENELOOP 3號：240分鐘（可調整60-360）
        maintenanceCycle: 60     // 維護週期：60天
    },
    eneloop_aaa: {
        chargingTime: 210,       // ENELOOP 4號：210分鐘（可調整60-300）
        maintenanceCycle: 60     // 維護週期：60天
    }
};
```

### 🌙 靜音時間設定

```javascript
const systemConfig = {
    quietHours: {
        enabled: true,
        start: '22:00',    // 晚上10點開始靜音
        end: '07:00'       // 早上7點結束靜音
    }
};
```

---

## 📊 使用場景

### 🔋 日常充電
1. 電池用完後，插入充電器
2. 手動開啟對應插座
3. 系統自動管理充電過程
4. 收到完成通知後取出電池

### 🔄 維護充電
- 系統每日早上8點自動檢查
- 超過維護週期自動開啟充電
- 無需人工干預

### 🛡️ 安全保護
- 防止過度充電損害電池
- 多重通知確保不遺漏
- 靜音時間避免夜間打擾

---

## 🆘 常見問題

### ❓ 通知沒有發送？
**檢查項目**：
- 通知服務是否正確配置
- 是否在靜音時間內
- Home Assistant通知服務是否正常

### ❓ 插座沒有自動關閉？
**檢查項目**：
- 實體ID是否正確
- Home Assistant連接是否正常
- 計時器是否正常啟動

### ❓ 維護充電沒有觸發？
**檢查項目**：
- 系統總開關是否開啟
- 自動維護開關是否開啟
- 定時觸發器是否正常

### ❓ 配置沒有生效？
**檢查項目**：
- 配置文件是否正確載入
- Node-RED是否重新部署
- 全域變數是否設定成功

---

## 📞 技術支援

### 🔍 檢查日誌
```bash
# Home Assistant 日誌
tail -f /config/home-assistant.log

# Node-RED 日誌
journalctl -u nodered -f
```

### 🔄 重新部署
1. 在Node-RED中點擊「Deploy」
2. 重新載入配置
3. 測試功能是否正常

### 🆘 緊急重置
如果系統出現問題：
1. 停用所有自動化
2. 手動關閉所有插座
3. 重新導入流程
4. 重新配置設定

---

## 🎉 享受智能充電！

現在您的充電電池管理系統已經準備就緒！

- ✅ 再也不用擔心過度充電
- ✅ 自動維護延長電池壽命
- ✅ 多重通知確保不遺漏
- ✅ 完全自動化的充電管理

**開始享受智能家居帶來的便利吧！** 🏠⚡🔋
