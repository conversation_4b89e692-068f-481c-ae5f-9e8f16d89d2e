[{"id": "battery_complete_tab", "type": "tab", "label": "智能充電電池管理系統 - 最終版", "disabled": false, "info": "完整的智能充電電池管理系統 - 最終修正版\n\n修正內容：\n1. 牧田電池domain改為switch\n2. 設備控制節點正確配置\n3. 修正通知服務配置\n4. 加回維護檢查和狀態報告功能\n5. 完整的debug輸出\n\n功能：\n1. 手動充電自動計時關閉\n2. 三個獨立的通知服務\n3. 維護充電檢查\n4. 系統狀態報告\n5. 完整的debug輸出\n\n支援設備：\n- 牧田BL1041B (switch.tp_link_power_strip_eb2f_cha_shang_4)\n- ENELOOP 3號 (switch.tp_link_power_strip_eb2f_cha_shang_5)\n- ENELOOP 4號 (switch.tp_link_power_strip_eb2f_cha_shang_6)\n\n通知服務：\n- NotifyHelper (notify.notify)\n- Synology Chat (notify.synology_chat_bot_3)\n- Telegram (notify.telegram)", "env": []}, {"id": "init_system", "type": "inject", "z": "battery_complete_tab", "name": "系統初始化", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": 3, "topic": "", "payload": "initialize", "payloadType": "str", "x": 120, "y": 60, "wires": [["setup_config"]]}, {"id": "setup_config", "type": "function", "z": "battery_complete_tab", "name": "載入配置", "func": "// 電池配置 - 最終修正版\nconst batteries = {\n    makita: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_4',\n        name: '牧田BL1041B充電電池',\n        chargingTime: 90,\n        maintenanceDays: 30,\n        domain: 'switch'\n    },\n    eneloop_aa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_5',\n        name: 'ENELOOP 3號充電電池',\n        chargingTime: 240,\n        maintenanceDays: 60,\n        domain: 'switch'\n    },\n    eneloop_aaa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_6',\n        name: 'ENELOOP 4號充電電池',\n        chargingTime: 210,\n        maintenanceDays: 60,\n        domain: 'switch'\n    }\n};\n\n// 通知服務配置\nconst notifications = {\n    services: ['notify.notify', 'notify.synology_chat_bot_3', 'notify.telegram'],\n    enabled: true,\n    quietHours: { start: 22, end: 7 }\n};\n\n// 儲存配置\nflow.set('batteries', batteries);\nflow.set('notifications', notifications);\n\n// 初始化充電記錄\nconst records = flow.get('chargingRecords') || {};\nObject.keys(batteries).forEach(key => {\n    if (!records[key]) {\n        records[key] = {\n            lastCharge: null,\n            count: 0\n        };\n    }\n});\nflow.set('chargingRecords', records);\n\nmsg.payload = {\n    status: 'initialized',\n    batteries: Object.keys(batteries).length,\n    notifications: notifications.services.length,\n    timestamp: new Date().toISOString(),\n    config: batteries\n};\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"系統已初始化\"});\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 60, "wires": [["debug_init"]]}, {"id": "debug_init", "type": "debug", "z": "battery_complete_tab", "name": "初始化Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 500, "y": 60, "wires": []}, {"id": "makita_state", "type": "server-state-changed", "z": "battery_complete_tab", "name": "牧田電池狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_4"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 140, "wires": [["charging_handler"]]}, {"id": "eneloop_aa_state", "type": "server-state-changed", "z": "battery_complete_tab", "name": "ENELOOP 3號狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_5"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 200, "wires": [["charging_handler"]]}, {"id": "eneloop_aaa_state", "type": "server-state-changed", "z": "battery_complete_tab", "name": "ENELOOP 4號狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_6"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 260, "wires": [["charging_handler"]]}, {"id": "charging_handler", "type": "function", "z": "battery_complete_tab", "name": "充電處理器", "func": "// 獲取配置\nconst batteries = flow.get('batteries');\nif (!batteries) {\n    node.error('系統未初始化，請先執行系統初始化');\n    return null;\n}\n\n// 解析狀態變化\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 找到對應的電池\nlet batteryKey = null;\nlet battery = null;\n\nfor (const [key, config] of Object.entries(batteries)) {\n    if (config.entityId === entityId) {\n        batteryKey = key;\n        battery = config;\n        break;\n    }\n}\n\nif (!battery) {\n    node.error(`找不到實體 ${entityId} 的配置`);\n    return null;\n}\n\n// 準備輸出\nconst outputs = [null, null, null]; // [通知, 控制, debug]\n\n// 檢查開啟狀態 (off -> on)\nif (oldState === 'off' && newState === 'on') {\n    // 清除現有計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n    }\n    \n    // 記錄開始時間\n    const startTime = new Date();\n    flow.set(`${batteryKey}_start`, startTime.getTime());\n    \n    // 更新充電記錄\n    const records = flow.get('chargingRecords') || {};\n    if (!records[batteryKey]) records[batteryKey] = {};\n    records[batteryKey].lastCharge = startTime.toISOString();\n    records[batteryKey].count = (records[batteryKey].count || 0) + 1;\n    flow.set('chargingRecords', records);\n    \n    // 設定自動關閉計時器\n    const chargingMs = battery.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 發送關閉命令\n        const controlMsg = {\n            payload: {\n                domain: battery.domain,\n                service: 'turn_off',\n                target: {\n                    entity_id: entityId\n                }\n            },\n            batteryKey: batteryKey,\n            action: 'auto_stop'\n        };\n        \n        // 發送到控制輸出\n        node.send([null, controlMsg, {\n            payload: `自動關閉 ${battery.name}`,\n            batteryKey: batteryKey,\n            action: 'auto_stop',\n            controlMsg: controlMsg\n        }]);\n        \n        flow.set(`${batteryKey}_timer`, null);\n    }, chargingMs);\n    \n    flow.set(`${batteryKey}_timer`, newTimerId);\n    \n    // 準備通知訊息\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${battery.name} 開始充電\\n⏱️ 預計時間：${battery.chargingTime}分鐘\\n🕐 開始時間：${startTime.toLocaleString('zh-TW')}`\n        },\n        batteryKey: batteryKey,\n        action: 'start'\n    };\n    \n    outputs[0] = notifyMsg;\n    outputs[2] = {\n        payload: `${battery.name} 開始充電`,\n        batteryKey: batteryKey,\n        chargingTime: battery.chargingTime,\n        timerId: newTimerId,\n        entityId: entityId,\n        domain: battery.domain\n    };\n}\n\n// 檢查關閉狀態 (on -> off)\nelse if (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set(`${batteryKey}_timer`, null);\n    }\n    \n    // 計算實際時間\n    const startTime = flow.get(`${batteryKey}_start`);\n    const actualTime = startTime ? Math.round((Date.now() - startTime) / 60000) : 0;\n    \n    // 準備完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${battery.name} 充電完成\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryKey: batteryKey,\n        action: 'complete'\n    };\n    \n    outputs[0] = notifyMsg;\n    outputs[2] = {\n        payload: `${battery.name} 充電完成`,\n        batteryKey: batteryKey,\n        actualTime: actualTime,\n        entityId: entityId\n    };\n}\n\nreturn outputs;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 200, "wires": [["notify_splitter"], ["device_control"], ["debug_charging"]]}, {"id": "notify_splitter", "type": "function", "z": "battery_complete_tab", "name": "通知分發器", "func": "// 檢查是否有通知訊息\nif (!msg || !msg.payload) {\n    return [null, null, null];\n}\n\n// 動態靜音時間控制\nlet quietMode = flow.get('quietMode');\nif (quietMode === undefined) {\n    quietMode = false; // 預設關閉靜音（測試模式）\n    flow.set('quietMode', quietMode);\n}\n\nlet isQuiet = false;\nif (quietMode) {\n    // 靜音模式開啟時，檢查時間\n    const now = new Date();\n    const hour = now.getHours();\n    isQuiet = hour >= 22 || hour < 7;\n}\n// 如果靜音模式關閉，isQuiet 保持 false\n\nif (isQuiet && msg.action !== 'error') {\n    return [null, null, null];\n}\n\nconst title = msg.payload.title;\nconst message = msg.payload.message;\n\n// 創建三個通知訊息 - 修正版\nconst notify1 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nconst notify2 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nconst notify3 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nreturn [notify1, notify2, notify3];", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 160, "wires": [["notify_helper"], ["synology_chat"], ["telegram_bot"]]}, {"id": "notify_helper", "type": "api-call-service", "z": "battery_complete_tab", "name": "NotifyHelper (ming)", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.notify_person", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"title\": payload.title, \"message\": payload.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "notify", "service": "notify_person", "x": 800, "y": 120, "wires": [["debug_notify1"]]}, {"id": "synology_chat", "type": "api-call-service", "z": "battery_complete_tab", "name": "Synology Chat", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.synology_chat_bot_3", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload.message, \"targets\": [\"person.ming\"]}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "notify", "service": "synology_chat_bot_3", "x": 800, "y": 160, "wires": [["debug_notify2"]]}, {"id": "telegram_bot", "type": "api-call-service", "z": "battery_complete_tab", "name": "Telegram", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.telegram", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"title\": payload.title, \"message\": payload.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "notify", "service": "telegram", "x": 800, "y": 200, "wires": [["debug_notify3"]]}, {"id": "device_control", "type": "api-call-service", "z": "battery_complete_tab", "name": "設備控制", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [{"property": "domain", "propertyType": "msg", "value": "payload.domain", "valueType": "jsonata"}, {"property": "service", "propertyType": "msg", "value": "payload.service", "valueType": "jsonata"}, {"property": "target", "propertyType": "msg", "value": "payload.target", "valueType": "jsonata"}], "queue": "none", "blockInputOverrides": false, "domain": "{{domain}}", "service": "{{service}}", "x": 580, "y": 240, "wires": [["debug_control"]]}, {"id": "maintenance_check", "type": "inject", "z": "battery_complete_tab", "name": "維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "0 9 * * *", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 120, "y": 380, "wires": [["maintenance_logic"]]}, {"id": "manual_maintenance", "type": "inject", "z": "battery_complete_tab", "name": "手動維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 130, "y": 420, "wires": [["maintenance_logic"]]}, {"id": "maintenance_logic", "type": "function", "z": "battery_complete_tab", "name": "維護檢查邏輯", "func": "// 獲取配置\nconst batteries = flow.get('batteries');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteries) {\n    node.error('系統未初始化');\n    return null;\n}\n\nconst now = new Date();\nconst maintenanceNeeded = [];\n\n// 檢查每個電池的維護需求\nfor (const [key, battery] of Object.entries(batteries)) {\n    const record = records[key];\n    \n    if (!record || !record.lastCharge) {\n        // 從未充電，需要維護\n        maintenanceNeeded.push({\n            key: key,\n            battery: battery,\n            reason: '從未充電',\n            daysSince: '未知'\n        });\n        continue;\n    }\n    \n    const lastCharge = new Date(record.lastCharge);\n    const daysSince = Math.floor((now - lastCharge) / (1000 * 60 * 60 * 24));\n    \n    if (daysSince >= battery.maintenanceDays) {\n        maintenanceNeeded.push({\n            key: key,\n            battery: battery,\n            reason: `超過${battery.maintenanceDays}天未充電`,\n            daysSince: daysSince\n        });\n    }\n}\n\n// 準備輸出\nconst outputs = [null, null, null]; // [通知, 控制, debug]\n\nif (maintenanceNeeded.length > 0) {\n    // 準備維護通知\n    const batteryList = maintenanceNeeded.map(item => \n        `• ${item.battery.name} (${item.daysSince}天前)`\n    ).join('\\n');\n    \n    const notifyMsg = {\n        payload: {\n            title: '🔧 電池維護提醒',\n            message: `🔧 以下電池需要維護充電：\\n${batteryList}\\n\\n建議進行維護充電以保持電池性能。`\n        },\n        action: 'maintenance_alert',\n        maintenanceList: maintenanceNeeded\n    };\n    \n    outputs[0] = notifyMsg;\n    \n    // 自動開啟第一個需要維護的電池\n    const firstBattery = maintenanceNeeded[0];\n    const controlMsg = {\n        payload: {\n            domain: firstBattery.battery.domain,\n            service: 'turn_on',\n            target: {\n                entity_id: firstBattery.battery.entityId\n            }\n        },\n        batteryKey: firstBattery.key,\n        action: 'maintenance_start'\n    };\n    \n    outputs[1] = controlMsg;\n} else {\n    // 無需維護\n    const notifyMsg = {\n        payload: {\n            title: '✅ 電池狀態良好',\n            message: '✅ 所有電池狀態良好，無需維護充電。'\n        },\n        action: 'maintenance_ok'\n    };\n    \n    outputs[0] = notifyMsg;\n}\n\noutputs[2] = {\n    payload: `維護檢查完成`,\n    maintenanceNeeded: maintenanceNeeded,\n    totalBatteries: Object.keys(batteries).length,\n    checkTime: now.toISOString()\n};\n\nreturn outputs;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 400, "wires": [["notify_splitter"], ["device_control"], ["debug_maintenance"]]}, {"id": "status_report", "type": "inject", "z": "battery_complete_tab", "name": "狀態報告", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "status_report", "payloadType": "str", "x": 120, "y": 480, "wires": [["status_logic"]]}, {"id": "status_logic", "type": "function", "z": "battery_complete_tab", "name": "狀態報告邏輯", "func": "// 獲取配置和記錄\nconst batteries = flow.get('batteries');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteries) {\n    node.error('系統未初始化');\n    return null;\n}\n\nconst now = new Date();\nlet statusReport = '📊 電池充電系統狀態報告\\n\\n';\n\n// 系統資訊\nstatusReport += `🕐 報告時間：${now.toLocaleString('zh-TW')}\\n`;\nstatusReport += `🔋 管理電池數量：${Object.keys(batteries).length}\\n\\n`;\n\n// 各電池狀態\nfor (const [key, battery] of Object.entries(batteries)) {\n    const record = records[key] || {};\n    \n    statusReport += `🔋 ${battery.name}\\n`;\n    statusReport += `   📍 插座：${battery.entityId}\\n`;\n    statusReport += `   ⏱️ 充電時間：${battery.chargingTime}分鐘\\n`;\n    statusReport += `   🔧 維護週期：${battery.maintenanceDays}天\\n`;\n    \n    if (record.lastCharge) {\n        const lastCharge = new Date(record.lastCharge);\n        const daysSince = Math.floor((now - lastCharge) / (1000 * 60 * 60 * 24));\n        statusReport += `   📅 上次充電：${lastCharge.toLocaleString('zh-TW')} (${daysSince}天前)\\n`;\n        statusReport += `   📈 充電次數：${record.count || 0}次\\n`;\n        \n        // 維護狀態\n        if (daysSince >= battery.maintenanceDays) {\n            statusReport += `   ⚠️ 狀態：需要維護充電\\n`;\n        } else {\n            statusReport += `   ✅ 狀態：良好\\n`;\n        }\n    } else {\n        statusReport += `   📅 上次充電：從未充電\\n`;\n        statusReport += `   📈 充電次數：0次\\n`;\n        statusReport += `   ⚠️ 狀態：需要初次充電\\n`;\n    }\n    \n    statusReport += '\\n';\n}\n\n// 準備通知訊息\nconst notifyMsg = {\n    payload: {\n        title: '📊 系統狀態報告',\n        message: statusReport\n    },\n    action: 'status_report'\n};\n\nconst debugMsg = {\n    payload: '狀態報告已生成',\n    batteries: batteries,\n    records: records,\n    reportTime: now.toISOString()\n};\n\nreturn [notifyMsg, null, debugMsg];", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 480, "wires": [["notify_splitter"], [], ["debug_status"]]}, {"id": "debug_charging", "type": "debug", "z": "battery_complete_tab", "name": "充電Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 580, "y": 280, "wires": []}, {"id": "debug_notify1", "type": "debug", "z": "battery_complete_tab", "name": "NotifyHelper Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1020, "y": 120, "wires": []}, {"id": "debug_notify2", "type": "debug", "z": "battery_complete_tab", "name": "Synology Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1010, "y": 160, "wires": []}, {"id": "debug_notify3", "type": "debug", "z": "battery_complete_tab", "name": "Telegram Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1010, "y": 200, "wires": []}, {"id": "debug_control", "type": "debug", "z": "battery_complete_tab", "name": "控制Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 760, "y": 240, "wires": []}, {"id": "debug_maintenance", "type": "debug", "z": "battery_complete_tab", "name": "維護Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 580, "y": 440, "wires": []}, {"id": "debug_status", "type": "debug", "z": "battery_complete_tab", "name": "狀態Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 500, "y": 520, "wires": []}, {"id": "test_notify", "type": "inject", "z": "battery_complete_tab", "name": "測試通知", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"title\":\"🧪 測試通知 (ming)\",\"message\":\"這是一個測試通知訊息，用於檢查通知服務是否正常運作。\\n\\n📱 NotifyHelper: 指定通知 ming (陣列格式)\\n💬 Synology Chat: 群組通知\\n📨 Telegram: 群組通知\\n\\n如果NotifyHelper仍有錯誤，請檢查Home Assistant中ming用戶的設定。\"}", "payloadType": "json", "x": 120, "y": 560, "wires": [["notify_splitter"]]}, {"id": "test_control", "type": "inject", "z": "battery_complete_tab", "name": "測試設備控制", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"domain\":\"switch\",\"service\":\"turn_off\",\"target\":{\"entity_id\":\"switch.tp_link_power_strip_eb2f_cha_shang_4\"}}", "payloadType": "json", "x": 130, "y": 600, "wires": [["device_control"]]}, {"id": "toggle_quiet_mode", "type": "inject", "z": "battery_complete_tab", "name": "切換靜音模式", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "toggle_quiet", "payloadType": "str", "x": 130, "y": 640, "wires": [["quiet_mode_logic"]]}, {"id": "quiet_mode_logic", "type": "function", "z": "battery_complete_tab", "name": "靜音模式控制", "func": "// 獲取當前靜音模式狀態\nlet quietMode = flow.get('quietMode');\nif (quietMode === undefined) {\n    quietMode = false; // 預設關閉靜音（測試模式）\n}\n\n// 切換靜音模式\nquietMode = !quietMode;\nflow.set('quietMode', quietMode);\n\n// 準備狀態訊息\nconst statusMsg = {\n    payload: {\n        title: quietMode ? '🔕 靜音模式已開啟' : '🔔 靜音模式已關閉',\n        message: quietMode ? \n            '🔕 靜音模式已開啟\\n⏰ 22:00-07:00 期間不會發送通知' : \n            '🔔 靜音模式已關閉\\n📱 所有時間都會發送通知（測試模式）'\n    },\n    action: 'quiet_mode_change'\n};\n\nconst debugMsg = {\n    payload: `靜音模式: ${quietMode ? '開啟' : '關閉'}`,\n    quietMode: quietMode,\n    timestamp: new Date().toISOString()\n};\n\nnode.status({\n    fill: quietMode ? \"yellow\" : \"green\",\n    shape: \"dot\",\n    text: quietMode ? \"靜音模式\" : \"正常模式\"\n});\n\nreturn [statusMsg, debugMsg];", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 320, "y": 640, "wires": [["notify_splitter"], ["debug_quiet"]]}, {"id": "debug_quiet", "type": "debug", "z": "battery_complete_tab", "name": "靜音Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 540, "y": 680, "wires": []}, {"id": "125d83f7.1a33dc", "type": "server", "name": "Home Assistant", "addon": true}]