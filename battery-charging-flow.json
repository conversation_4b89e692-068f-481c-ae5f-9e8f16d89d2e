[{"id": "battery_tab", "type": "tab", "label": "智能充電電池管理系統", "disabled": false, "info": "智能充電電池管理系統\n\n功能：\n1. 手動充電自動計時關閉\n2. 定期維護充電\n3. 多重通知系統\n4. 安全保護機制\n\n支援電池：\n- 牧田BL1041B (HS300 2-4插座)\n- ENELOOP 3號 (HS300 2-5插座)\n- ENELOOP 4號 (HS300 2-6插座)", "env": []}, {"id": "makita_monitor", "type": "server-state-changed", "z": "battery_tab", "name": "牧田電池插座監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["media_player.volume_set"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 150, "y": 100, "wires": [["makita_logic"]]}, {"id": "eneloop_aa_monitor", "type": "server-state-changed", "z": "battery_tab", "name": "ENELOOP 3號插座監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_5"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 150, "y": 200, "wires": [["eneloop_aa_logic"]]}, {"id": "eneloop_aaa_monitor", "type": "server-state-changed", "z": "battery_tab", "name": "ENELOOP 4號插座監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_6"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 150, "y": 300, "wires": [["eneloop_aaa_logic"]]}, {"id": "makita_logic", "type": "function", "z": "battery_tab", "name": "牧田充電邏輯", "func": "// 載入配置\nconst config = global.get('batteryChargingConfig') || {};\nconst batteryConfig = config.batteryConfig?.makita || {\n    chargingTime: 90,\n    maxChargingTime: 180,\n    friendlyName: '牧田BL1041B充電電池',\n    location: 'HS300 2-4插座'\n};\n\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 檢查是否為手動開啟 (從off變為on)\nif (oldState === 'off' && newState === 'on') {\n    // 檢查是否已有計時器在運行\n    const timerId = flow.get('makita_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('makita_timer_id', null);\n    }\n    \n    // 記錄充電開始時間\n    const startTime = new Date();\n    flow.set('makita_charge_start', startTime.getTime());\n    \n    // 設定充電計時器\n    const chargingTimeMs = batteryConfig.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 時間到，自動關閉插座\n        const stopMsg = {\n            payload: {\n                domain: 'media_player',\n                service: 'turn_off',\n                data: {},\n                target: {\n                    entity_id: entityId\n                }\n            },\n            batteryType: 'makita',\n            action: 'auto_stop',\n            chargingTime: batteryConfig.chargingTime\n        };\n        node.send([null, stopMsg, null]);\n        flow.set('makita_timer_id', null);\n    }, chargingTimeMs);\n    \n    flow.set('makita_timer_id', newTimerId);\n    \n    // 發送充電開始通知\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${batteryConfig.friendlyName} 開始充電\\n📍 位置：${batteryConfig.location}\\n⏱️ 預計充電時間：${batteryConfig.chargingTime}分鐘\\n🕐 開始時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'makita',\n        action: 'charging_started'\n    };\n    \n    // 更新Home Assistant記錄\n    const updateMsg = {\n        payload: {\n            domain: 'input_datetime',\n            service: 'set_datetime',\n            data: {\n                datetime: new Date().toISOString().slice(0, 19)\n            },\n            target: {\n                entity_id: 'input_datetime.makita_last_charge'\n            }\n        }\n    };\n    \n    return [notifyMsg, null, updateMsg];\n}\n\n// 檢查是否為關閉狀態\nif (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get('makita_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('makita_timer_id', null);\n    }\n    \n    // 計算實際充電時間\n    const startTime = flow.get('makita_charge_start');\n    const actualTime = startTime ? Math.round((new Date().getTime() - startTime) / 60000) : 0;\n    \n    // 發送充電完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${batteryConfig.friendlyName} 充電完成\\n📍 位置：${batteryConfig.location}\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'makita',\n        action: 'charging_completed',\n        actualTime: actualTime\n    };\n    \n    return [notifyMsg, null, null];\n}\n\nreturn null;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 100, "wires": [["notification_sender"], ["device_controller"], ["ha_updater"]]}, {"id": "eneloop_aa_logic", "type": "function", "z": "battery_tab", "name": "ENELOOP 3號充電邏輯", "func": "// 載入配置\nconst config = global.get('batteryChargingConfig') || {};\nconst batteryConfig = config.batteryConfig?.eneloop_aa || {\n    chargingTime: 240,\n    maxChargingTime: 360,\n    friendlyName: 'ENELOOP 3號充電電池',\n    location: 'HS300 2-5插座'\n};\n\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 檢查是否為手動開啟 (從off變為on)\nif (oldState === 'off' && newState === 'on') {\n    // 檢查是否已有計時器在運行\n    const timerId = flow.get('eneloop_aa_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('eneloop_aa_timer_id', null);\n    }\n    \n    // 記錄充電開始時間\n    const startTime = new Date();\n    flow.set('eneloop_aa_charge_start', startTime.getTime());\n    \n    // 設定充電計時器\n    const chargingTimeMs = batteryConfig.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 時間到，自動關閉插座\n        const stopMsg = {\n            payload: {\n                domain: 'switch',\n                service: 'turn_off',\n                data: {},\n                target: {\n                    entity_id: entityId\n                }\n            },\n            batteryType: 'eneloop_aa',\n            action: 'auto_stop',\n            chargingTime: batteryConfig.chargingTime\n        };\n        node.send([null, stopMsg, null]);\n        flow.set('eneloop_aa_timer_id', null);\n    }, chargingTimeMs);\n    \n    flow.set('eneloop_aa_timer_id', newTimerId);\n    \n    // 發送充電開始通知\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${batteryConfig.friendlyName} 開始充電\\n📍 位置：${batteryConfig.location}\\n⏱️ 預計充電時間：${batteryConfig.chargingTime}分鐘\\n🕐 開始時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'eneloop_aa',\n        action: 'charging_started'\n    };\n    \n    // 更新Home Assistant記錄\n    const updateMsg = {\n        payload: {\n            domain: 'input_datetime',\n            service: 'set_datetime',\n            data: {\n                datetime: new Date().toISOString().slice(0, 19)\n            },\n            target: {\n                entity_id: 'input_datetime.eneloop_aa_last_charge'\n            }\n        }\n    };\n    \n    return [notifyMsg, null, updateMsg];\n}\n\n// 檢查是否為關閉狀態\nif (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get('eneloop_aa_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('eneloop_aa_timer_id', null);\n    }\n    \n    // 計算實際充電時間\n    const startTime = flow.get('eneloop_aa_charge_start');\n    const actualTime = startTime ? Math.round((new Date().getTime() - startTime) / 60000) : 0;\n    \n    // 發送充電完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${batteryConfig.friendlyName} 充電完成\\n📍 位置：${batteryConfig.location}\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'eneloop_aa',\n        action: 'charging_completed',\n        actualTime: actualTime\n    };\n    \n    return [notifyMsg, null, null];\n}\n\nreturn null;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 200, "wires": [["notification_sender"], ["device_controller"], ["ha_updater"]]}, {"id": "eneloop_aaa_logic", "type": "function", "z": "battery_tab", "name": "ENELOOP 4號充電邏輯", "func": "// 載入配置\nconst config = global.get('batteryChargingConfig') || {};\nconst batteryConfig = config.batteryConfig?.eneloop_aaa || {\n    chargingTime: 210,\n    maxChargingTime: 300,\n    friendlyName: 'ENELOOP 4號充電電池',\n    location: 'HS300 2-6插座'\n};\n\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 檢查是否為手動開啟 (從off變為on)\nif (oldState === 'off' && newState === 'on') {\n    // 檢查是否已有計時器在運行\n    const timerId = flow.get('eneloop_aaa_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('eneloop_aaa_timer_id', null);\n    }\n    \n    // 記錄充電開始時間\n    const startTime = new Date();\n    flow.set('eneloop_aaa_charge_start', startTime.getTime());\n    \n    // 設定充電計時器\n    const chargingTimeMs = batteryConfig.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 時間到，自動關閉插座\n        const stopMsg = {\n            payload: {\n                domain: 'switch',\n                service: 'turn_off',\n                data: {},\n                target: {\n                    entity_id: entityId\n                }\n            },\n            batteryType: 'eneloop_aaa',\n            action: 'auto_stop',\n            chargingTime: batteryConfig.chargingTime\n        };\n        node.send([null, stopMsg, null]);\n        flow.set('eneloop_aaa_timer_id', null);\n    }, chargingTimeMs);\n    \n    flow.set('eneloop_aaa_timer_id', newTimerId);\n    \n    // 發送充電開始通知\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${batteryConfig.friendlyName} 開始充電\\n📍 位置：${batteryConfig.location}\\n⏱️ 預計充電時間：${batteryConfig.chargingTime}分鐘\\n🕐 開始時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'eneloop_aaa',\n        action: 'charging_started'\n    };\n    \n    // 更新Home Assistant記錄\n    const updateMsg = {\n        payload: {\n            domain: 'input_datetime',\n            service: 'set_datetime',\n            data: {\n                datetime: new Date().toISOString().slice(0, 19)\n            },\n            target: {\n                entity_id: 'input_datetime.eneloop_aaa_last_charge'\n            }\n        }\n    };\n    \n    return [notifyMsg, null, updateMsg];\n}\n\n// 檢查是否為關閉狀態\nif (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get('eneloop_aaa_timer_id');\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set('eneloop_aaa_timer_id', null);\n    }\n    \n    // 計算實際充電時間\n    const startTime = flow.get('eneloop_aaa_charge_start');\n    const actualTime = startTime ? Math.round((new Date().getTime() - startTime) / 60000) : 0;\n    \n    // 發送充電完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${batteryConfig.friendlyName} 充電完成\\n📍 位置：${batteryConfig.location}\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryType: 'eneloop_aaa',\n        action: 'charging_completed',\n        actualTime: actualTime\n    };\n    \n    return [notifyMsg, null, null];\n}\n\nreturn null;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 300, "wires": [["notification_sender"], ["device_controller"], ["ha_updater"]]}, {"id": "maintenance_check", "type": "inject", "z": "battery_tab", "name": "每日維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "00 08 * * *", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 150, "y": 450, "wires": [["maintenance_logic"]]}, {"id": "maintenance_logic", "type": "function", "z": "battery_tab", "name": "維護充電邏輯", "func": "// 載入配置\nconst config = global.get('batteryChargingConfig') || {};\nconst batteries = config.batteryConfig || {};\n\n// 檢查系統是否啟用\nconst systemEnabled = global.get('battery_management_enabled') !== false;\nconst autoMaintenance = global.get('battery_auto_maintenance') !== false;\n\nif (!systemEnabled || !autoMaintenance) {\n    return null;\n}\n\n// 獲取當前時間\nconst now = new Date();\nconst messages = [];\n\n// 檢查每個電池的維護需求\nObject.keys(batteries).forEach(batteryKey => {\n    const battery = batteries[batteryKey];\n    const lastChargeKey = `${batteryKey}_last_charge`;\n    const lastChargeTime = flow.get(lastChargeKey);\n    \n    if (lastChargeTime) {\n        const daysSinceCharge = Math.floor((now.getTime() - lastChargeTime) / (1000 * 60 * 60 * 24));\n        \n        // 檢查是否需要維護充電\n        if (daysSinceCharge >= battery.maintenanceCycle) {\n            // 創建維護充電訊息\n            const maintenanceMsg = {\n                payload: {\n                    domain: battery.entityId.includes('switch') ? 'switch' : 'media_player',\n                    service: 'turn_on',\n                    data: {},\n                    target: {\n                        entity_id: battery.entityId\n                    }\n                },\n                batteryType: batteryKey,\n                action: 'maintenance_charging',\n                daysSinceLastCharge: daysSinceCharge\n            };\n            \n            // 創建通知訊息\n            const notifyMsg = {\n                payload: {\n                    title: '🔄 維護充電',\n                    message: `🔄 ${battery.friendlyName} 開始維護充電\\n📍 位置：${battery.location}\\n📅 距離上次充電：${daysSinceCharge}天\\n⏱️ 預計充電時間：${battery.chargingTime}分鐘`\n                },\n                batteryType: batteryKey,\n                action: 'maintenance_started'\n            };\n            \n            messages.push([notifyMsg, maintenanceMsg]);\n        }\n    }\n});\n\n// 如果有需要維護的電池，發送訊息\nif (messages.length > 0) {\n    return messages;\n}\n\nreturn null;", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 450, "wires": [["notification_sender"], ["device_controller"]]}, {"id": "notification_sender", "type": "function", "z": "battery_tab", "name": "通知發送器", "func": "// 載入配置\nconst config = global.get('batteryChargingConfig') || {};\nconst notificationConfig = config.notificationConfig || {};\nconst utils = config.utils || {};\n\n// 檢查是否在靜音時間\nif (utils.isQuietHours && utils.isQuietHours()) {\n    // 在靜音時間，只發送緊急通知\n    if (msg.action !== 'charging_error' && msg.action !== 'safety_stop') {\n        return null;\n    }\n}\n\n// 準備通知訊息\nconst title = msg.payload.title || '電池管理通知';\nconst message = msg.payload.message || '電池狀態變更';\n\n// 創建多個輸出訊息\nconst messages = [];\n\n// NotifyHelper 通知\nif (notificationConfig.notifyHelper?.enabled) {\n    messages.push({\n        payload: {\n            domain: 'notify',\n            service: notificationConfig.notifyHelper.service || 'notify',\n            data: {\n                title: title,\n                message: message,\n                data: {\n                    priority: notificationConfig.notifyHelper.priority || 'normal'\n                }\n            }\n        }\n    });\n}\n\n// Synology Chat 通知\nif (notificationConfig.synologyChat?.enabled) {\n    messages.push({\n        payload: {\n            domain: 'notify',\n            service: notificationConfig.synologyChat.service || 'synology_chat_bot_3',\n            data: {\n                title: title,\n                message: message\n            }\n        }\n    });\n}\n\n// Telegram 通知\nif (notificationConfig.telegram?.enabled) {\n    messages.push({\n        payload: {\n            domain: 'notify',\n            service: notificationConfig.telegram.service || 'telegram',\n            data: {\n                title: title,\n                message: message,\n                data: {\n                    parse_mode: 'HTML'\n                }\n            }\n        }\n    });\n}\n\nreturn messages;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 650, "y": 250, "wires": [["notify_helper"], ["synology_chat"], ["telegram_notify"]]}, {"id": "device_controller", "type": "api-call-service", "z": "battery_tab", "name": "設備控制器", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "{{payload.target.entity_id}}", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 650, "y": 350, "wires": [[]]}, {"id": "ha_updater", "type": "api-call-service", "z": "battery_tab", "name": "HA記錄更新", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "{{payload.target.entity_id}}", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 650, "y": 150, "wires": [[]]}, {"id": "notify_helper", "type": "api-call-service", "z": "battery_tab", "name": "NotifyHelper", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 850, "y": 200, "wires": [[]]}, {"id": "synology_chat", "type": "api-call-service", "z": "battery_tab", "name": "Synology Chat", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 850, "y": 250, "wires": [[]]}, {"id": "telegram_notify", "type": "api-call-service", "z": "battery_tab", "name": "Telegram", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 850, "y": 300, "wires": [[]]}, {"id": "home_assistant_server", "type": "server", "name": "Home Assistant", "version": 4, "addon": true, "rejectUnauthorizedCerts": true, "ha_boolean": "y|yes|true|on|home|open", "connectionDelay": true, "cacheJson": true, "heartbeat": false, "heartbeatInterval": 30}]