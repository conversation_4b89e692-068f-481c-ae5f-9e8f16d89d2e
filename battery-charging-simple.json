[{"id": "battery_simple_tab", "type": "tab", "label": "智能充電電池管理系統 - 簡化版", "disabled": false, "info": "純Node-RED實現的智能充電電池管理系統\n\n功能：\n1. 手動充電自動計時關閉\n2. 定期維護充電\n3. 多重通知系統\n4. 完整的debug輸出\n\n支援電池：\n- 牧田BL1041B (media_player.volume_set)\n- ENELOOP 3號 (switch.tp_link_power_strip_eb2f_cha_shang_5)\n- ENELOOP 4號 (switch.tp_link_power_strip_eb2f_cha_shang_6)\n\n無需yaml文件，純Node-RED實現", "env": []}, {"id": "init_config", "type": "inject", "z": "battery_simple_tab", "name": "初始化配置", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": 2, "topic": "", "payload": "init", "payloadType": "str", "x": 120, "y": 60, "wires": [["config_setup"]]}, {"id": "config_setup", "type": "function", "z": "battery_simple_tab", "name": "配置設定", "func": "// 電池配置\nconst batteryConfig = {\n    makita: {\n        entityId: 'media_player.volume_set',\n        name: '牧田BL1041B充電電池',\n        chargingTime: 90, // 分鐘\n        maintenanceDays: 30,\n        maxTime: 180\n    },\n    eneloop_aa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_5',\n        name: 'ENELOOP 3號充電電池',\n        chargingTime: 240, // 分鐘\n        maintenanceDays: 60,\n        maxTime: 360\n    },\n    eneloop_aaa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_6',\n        name: 'ENELOOP 4號充電電池',\n        chargingTime: 210, // 分鐘\n        maintenanceDays: 60,\n        maxTime: 300\n    }\n};\n\n// 通知配置\nconst notifyConfig = {\n    services: [\n        'notify.notify',\n        'notify.synology_chat_bot_3',\n        'notify.telegram'\n    ],\n    enabled: true\n};\n\n// 儲存到context\nflow.set('batteryConfig', batteryConfig);\nflow.set('notifyConfig', notifyConfig);\n\n// 初始化充電記錄\nconst chargingRecords = flow.get('chargingRecords') || {};\nObject.keys(batteryConfig).forEach(key => {\n    if (!chargingRecords[key]) {\n        chargingRecords[key] = {\n            lastCharge: null,\n            chargingCount: 0\n        };\n    }\n});\nflow.set('chargingRecords', chargingRecords);\n\nmsg.payload = {\n    status: 'initialized',\n    config: batteryConfig,\n    notify: notifyConfig\n};\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"配置已載入\"});\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 60, "wires": [["debug_config"]]}, {"id": "debug_config", "type": "debug", "z": "battery_simple_tab", "name": "配置Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 500, "y": 60, "wires": []}, {"id": "makita_monitor", "type": "server-state-changed", "z": "battery_simple_tab", "name": "牧田電池監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["media_player.volume_set"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 140, "wires": [["battery_logic"]]}, {"id": "eneloop_aa_monitor", "type": "server-state-changed", "z": "battery_simple_tab", "name": "ENELOOP 3號監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_5"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 200, "wires": [["battery_logic"]]}, {"id": "eneloop_aaa_monitor", "type": "server-state-changed", "z": "battery_simple_tab", "name": "ENELOOP 4號監控", "server": "home_assistant_server", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_6"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 120, "y": 260, "wires": [["battery_logic"]]}, {"id": "battery_logic", "type": "function", "z": "battery_simple_tab", "name": "電池充電邏輯", "func": "// 獲取配置\nconst batteryConfig = flow.get('batteryConfig');\nconst notifyConfig = flow.get('notifyConfig');\n\nif (!batteryConfig || !notifyConfig) {\n    node.error('配置未載入，請先執行初始化');\n    return null;\n}\n\n// 解析狀態變化\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 找到對應的電池配置\nlet batteryKey = null;\nlet battery = null;\n\nfor (const [key, config] of Object.entries(batteryConfig)) {\n    if (config.entityId === entityId) {\n        batteryKey = key;\n        battery = config;\n        break;\n    }\n}\n\nif (!battery) {\n    node.error(`找不到實體 ${entityId} 的配置`);\n    return null;\n}\n\n// 準備輸出訊息\nconst messages = [null, null, null]; // [通知, 控制, debug]\n\n// 檢查是否為手動開啟 (從off變為on)\nif (oldState === 'off' && newState === 'on') {\n    // 清除現有計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set(`${batteryKey}_timer`, null);\n    }\n    \n    // 記錄充電開始時間\n    const startTime = new Date();\n    flow.set(`${batteryKey}_start`, startTime.getTime());\n    \n    // 更新充電記錄\n    const records = flow.get('chargingRecords') || {};\n    if (!records[batteryKey]) records[batteryKey] = {};\n    records[batteryKey].lastCharge = startTime.toISOString();\n    records[batteryKey].chargingCount = (records[batteryKey].chargingCount || 0) + 1;\n    flow.set('chargingRecords', records);\n    \n    // 設定自動關閉計時器\n    const chargingTimeMs = battery.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 自動關閉插座\n        const domain = entityId.includes('switch') ? 'switch' : 'media_player';\n        const service = 'turn_off';\n        \n        const controlMsg = {\n            payload: {\n                domain: domain,\n                service: service,\n                target: {\n                    entity_id: entityId\n                }\n            },\n            topic: 'auto_stop',\n            batteryKey: batteryKey\n        };\n        \n        // 發送控制訊息\n        node.send([null, controlMsg, {\n            payload: `自動關閉 ${battery.name}`,\n            topic: 'auto_stop'\n        }]);\n        \n        // 清除計時器\n        flow.set(`${batteryKey}_timer`, null);\n    }, chargingTimeMs);\n    \n    flow.set(`${batteryKey}_timer`, newTimerId);\n    \n    // 準備充電開始通知\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${battery.name} 開始充電\\n⏱️ 預計充電時間：${battery.chargingTime}分鐘\\n🕐 開始時間：${startTime.toLocaleString('zh-TW')}`\n        },\n        topic: 'charging_started',\n        batteryKey: batteryKey\n    };\n    \n    messages[0] = notifyMsg;\n    messages[2] = {\n        payload: `${battery.name} 開始充電，計時器已設定 ${battery.chargingTime} 分鐘`,\n        topic: 'charging_started',\n        batteryKey: batteryKey,\n        timerId: newTimerId\n    };\n}\n\n// 檢查是否為關閉狀態\nelse if (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set(`${batteryKey}_timer`, null);\n    }\n    \n    // 計算實際充電時間\n    const startTime = flow.get(`${batteryKey}_start`);\n    const actualTime = startTime ? Math.round((new Date().getTime() - startTime) / 60000) : 0;\n    \n    // 準備充電完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${battery.name} 充電完成\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        topic: 'charging_completed',\n        batteryKey: batteryKey\n    };\n    \n    messages[0] = notifyMsg;\n    messages[2] = {\n        payload: `${battery.name} 充電完成，實際時間：${actualTime}分鐘`,\n        topic: 'charging_completed',\n        batteryKey: batteryKey,\n        actualTime: actualTime\n    };\n}\n\n// 過濾空訊息\nconst filteredMessages = messages.map(msg => msg || null);\n\nreturn filteredMessages;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 200, "wires": [["notification_handler"], ["device_controller"], ["debug_battery"]]}, {"id": "notification_handler", "type": "function", "z": "battery_simple_tab", "name": "通知處理器", "func": "// 獲取通知配置\nconst notifyConfig = flow.get('notifyConfig');\n\nif (!notifyConfig || !notifyConfig.enabled) {\n    return null;\n}\n\n// 檢查是否在靜音時間 (22:00-07:00)\nconst now = new Date();\nconst hour = now.getHours();\nconst isQuietTime = hour >= 22 || hour < 7;\n\nif (isQuietTime && msg.topic !== 'error') {\n    // 靜音時間只發送錯誤通知\n    return null;\n}\n\nconst title = msg.payload.title || '電池管理通知';\nconst message = msg.payload.message || '狀態變更';\n\n// 為每個通知服務創建訊息\nconst messages = [];\n\nnotifyConfig.services.forEach(service => {\n    messages.push({\n        payload: {\n            domain: 'notify',\n            service: service.replace('notify.', ''),\n            data: {\n                title: title,\n                message: message\n            }\n        },\n        topic: msg.topic,\n        service: service\n    });\n});\n\nreturn [messages];", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 160, "wires": [["split_notifications"]]}, {"id": "split_notifications", "type": "split", "z": "battery_simple_tab", "name": "分割通知", "splt": "\\n", "spltType": "str", "arraySplt": 1, "arraySpltType": "len", "stream": false, "addname": "", "x": 760, "y": 160, "wires": [["send_notification"]]}, {"id": "send_notification", "type": "api-call-service", "z": "battery_simple_tab", "name": "發送通知", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "", "data": "{{payload.data}}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 920, "y": 160, "wires": [["debug_notification"]]}, {"id": "device_controller", "type": "api-call-service", "z": "battery_simple_tab", "name": "設備控制器", "server": "home_assistant_server", "version": 6, "debugenabled": false, "domain": "{{payload.domain}}", "service": "{{payload.service}}", "areaId": [], "deviceId": [], "entityId": "{{payload.target.entity_id}}", "data": "{}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 580, "y": 240, "wires": [["debug_control"]]}, {"id": "debug_battery", "type": "debug", "z": "battery_simple_tab", "name": "電池Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 580, "y": 300, "wires": []}, {"id": "debug_notification", "type": "debug", "z": "battery_simple_tab", "name": "通知Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1100, "y": 160, "wires": []}, {"id": "debug_control", "type": "debug", "z": "battery_simple_tab", "name": "控制Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 760, "y": 240, "wires": []}, {"id": "maintenance_check", "type": "inject", "z": "battery_simple_tab", "name": "維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "00 08 * * *", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 120, "y": 380, "wires": [["maintenance_logic"]]}, {"id": "maintenance_logic", "type": "function", "z": "battery_simple_tab", "name": "維護充電邏輯", "func": "// 獲取配置\nconst batteryConfig = flow.get('batteryConfig');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteryConfig) {\n    node.error('配置未載入');\n    return null;\n}\n\nconst now = new Date();\nconst messages = [];\n\n// 檢查每個電池的維護需求\nObject.keys(batteryConfig).forEach(batteryKey => {\n    const battery = batteryConfig[batteryKey];\n    const record = records[batteryKey];\n    \n    if (!record || !record.lastCharge) {\n        // 沒有充電記錄，跳過\n        return;\n    }\n    \n    const lastChargeDate = new Date(record.lastCharge);\n    const daysSinceCharge = Math.floor((now.getTime() - lastChargeDate.getTime()) / (1000 * 60 * 60 * 24));\n    \n    // 檢查是否需要維護充電\n    if (daysSinceCharge >= battery.maintenanceDays) {\n        // 需要維護充電\n        const domain = battery.entityId.includes('switch') ? 'switch' : 'media_player';\n        \n        const controlMsg = {\n            payload: {\n                domain: domain,\n                service: 'turn_on',\n                target: {\n                    entity_id: battery.entityId\n                }\n            },\n            topic: 'maintenance_charging',\n            batteryKey: batteryKey\n        };\n        \n        const notifyMsg = {\n            payload: {\n                title: '🔄 維護充電',\n                message: `🔄 ${battery.name} 開始維護充電\\n📅 距離上次充電：${daysSinceCharge}天\\n⏱️ 預計充電時間：${battery.chargingTime}分鐘`\n            },\n            topic: 'maintenance_started',\n            batteryKey: batteryKey\n        };\n        \n        const debugMsg = {\n            payload: `維護充電：${battery.name}，距離上次充電 ${daysSinceCharge} 天`,\n            topic: 'maintenance_check',\n            batteryKey: batteryKey,\n            daysSinceCharge: daysSinceCharge\n        };\n        \n        messages.push([notifyMsg, controlMsg, debugMsg]);\n    }\n});\n\n// 如果有需要維護的電池\nif (messages.length > 0) {\n    return messages;\n}\n\n// 沒有需要維護的電池\nreturn [null, null, {\n    payload: '維護檢查完成，無需維護充電',\n    topic: 'maintenance_check',\n    timestamp: now.toISOString()\n}];", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 320, "y": 380, "wires": [["notification_handler"], ["device_controller"], ["debug_maintenance"]]}, {"id": "debug_maintenance", "type": "debug", "z": "battery_simple_tab", "name": "維護Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 580, "y": 380, "wires": []}, {"id": "manual_maintenance", "type": "inject", "z": "battery_simple_tab", "name": "手動維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "manual_maintenance_check", "payloadType": "str", "x": 120, "y": 440, "wires": [["maintenance_logic"]]}, {"id": "view_records", "type": "inject", "z": "battery_simple_tab", "name": "查看充電記錄", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "view_records", "payloadType": "str", "x": 120, "y": 500, "wires": [["show_records"]]}, {"id": "show_records", "type": "function", "z": "battery_simple_tab", "name": "顯示記錄", "func": "const batteryConfig = flow.get('batteryConfig');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteryConfig) {\n    msg.payload = '配置未載入';\n    return msg;\n}\n\nconst now = new Date();\nlet report = '📊 充電記錄報告\\n\\n';\n\nObject.keys(batteryConfig).forEach(batteryKey => {\n    const battery = batteryConfig[batteryKey];\n    const record = records[batteryKey] || {};\n    \n    report += `🔋 ${battery.name}\\n`;\n    \n    if (record.lastCharge) {\n        const lastChargeDate = new Date(record.lastCharge);\n        const daysSinceCharge = Math.floor((now.getTime() - lastChargeDate.getTime()) / (1000 * 60 * 60 * 24));\n        \n        report += `   📅 上次充電：${lastChargeDate.toLocaleString('zh-TW')}\\n`;\n        report += `   ⏰ 距今天數：${daysSinceCharge}天\\n`;\n        report += `   🔄 維護週期：${battery.maintenanceDays}天\\n`;\n        report += `   📈 充電次數：${record.chargingCount || 0}次\\n`;\n        \n        if (daysSinceCharge >= battery.maintenanceDays) {\n            report += `   ⚠️ 需要維護充電\\n`;\n        } else {\n            const daysLeft = battery.maintenanceDays - daysSinceCharge;\n            report += `   ✅ 還有${daysLeft}天需要維護\\n`;\n        }\n    } else {\n        report += `   ❌ 無充電記錄\\n`;\n    }\n    \n    report += '\\n';\n});\n\nmsg.payload = report;\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 320, "y": 500, "wires": [["debug_records"]]}, {"id": "debug_records", "type": "debug", "z": "battery_simple_tab", "name": "記錄Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 500, "y": 500, "wires": []}, {"id": "home_assistant_server", "type": "server", "name": "Home Assistant", "version": 4, "addon": true, "rejectUnauthorizedCerts": true, "ha_boolean": "y|yes|true|on|home|open", "connectionDelay": true, "cacheJson": true, "heartbeat": false, "heartbeatInterval": 30}]