[{"id": "7aa6bd9d6c51a86b", "type": "tab", "label": "智能充電電池管理系統 - 用戶修正版", "disabled": false, "info": "完整的智能充電電池管理系統 - 用戶修正版\n\n修正內容：\n1. 修正NotifyHelper的JSONata語法錯誤\n2. 使用正確的notify.notify_person配置\n3. 保持所有原有功能\n4. 完整的debug輸出\n\n功能：\n1. 手動充電自動計時關閉\n2. 三個獨立的通知服務\n3. 維護充電檢查\n4. 系統狀態報告\n5. 靜音模式控制\n6. 完整的debug輸出\n\n支援設備：\n- 牧田BL1041B (switch.tp_link_power_strip_eb2f_cha_shang_4)\n- ENELOOP 3號 (switch.tp_link_power_strip_eb2f_cha_shang_5)\n- ENELOOP 4號 (switch.tp_link_power_strip_eb2f_cha_shang_6)\n\n通知服務：\n- NotifyHelper (notify.notify_person → person.ming)\n- Synology Chat (notify.synology_chat_bot_3)\n- Telegram (notify.telegram)", "env": []}, {"id": "315654b802f3d18d", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "系統初始化", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": 3, "topic": "", "payload": "initialize", "payloadType": "str", "x": 110, "y": 20, "wires": [["3ae6ce8d279bf08c"]]}, {"id": "3ae6ce8d279bf08c", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "載入配置", "func": "// 電池配置 - 最終修正版\nconst batteries = {\n    makita: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_4',\n        name: '牧田BL1041B充電電池',\n        chargingTime: 90,\n        maintenanceDays: 30,\n        domain: 'switch'\n    },\n    eneloop_aa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_5',\n        name: 'ENELOOP 3號充電電池',\n        chargingTime: 240,\n        maintenanceDays: 60,\n        domain: 'switch'\n    },\n    eneloop_aaa: {\n        entityId: 'switch.tp_link_power_strip_eb2f_cha_shang_6',\n        name: 'ENELOOP 4號充電電池',\n        chargingTime: 210,\n        maintenanceDays: 60,\n        domain: 'switch'\n    }\n};\n\n// 通知服務配置\nconst notifications = {\n    services: ['notify.notify_person', 'notify.synology_chat_bot_3', 'notify.telegram'],\n    enabled: true,\n    quietHours: { start: 22, end: 7 }\n};\n\n// 儲存配置\nflow.set('batteries', batteries);\nflow.set('notifications', notifications);\nflow.set('quietMode', false); // 預設關閉靜音（測試模式）\n\n// 初始化充電記錄\nconst records = flow.get('chargingRecords') || {};\nObject.keys(batteries).forEach(key => {\n    if (!records[key]) {\n        records[key] = {\n            lastCharge: null,\n            count: 0\n        };\n    }\n});\nflow.set('chargingRecords', records);\n\nmsg.payload = {\n    status: 'initialized',\n    batteries: Object.keys(batteries).length,\n    notifications: notifications.services.length,\n    timestamp: new Date().toISOString(),\n    config: batteries\n};\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"系統已初始化\"});\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 290, "y": 20, "wires": [["33a602c10671f62e"]]}, {"id": "33a602c10671f62e", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "初始化Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 490, "y": 20, "wires": []}, {"id": "f8f9921580746cd5", "type": "server-state-changed", "z": "7aa6bd9d6c51a86b", "name": "牧田電池狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_4"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 90, "y": 220, "wires": [["557a2b498f2cdeed"]]}, {"id": "ac19e8514a8341ac", "type": "server-state-changed", "z": "7aa6bd9d6c51a86b", "name": "ENELOOP 3號狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_5"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 110, "y": 280, "wires": [["557a2b498f2cdeed"]]}, {"id": "899db7def56ccae2", "type": "server-state-changed", "z": "7aa6bd9d6c51a86b", "name": "ENELOOP 4號狀態", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["switch.tp_link_power_strip_eb2f_cha_shang_6"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": true, "ignorePrevStateUnknown": true, "ignorePrevStateUnavailable": true, "ignoreCurrentStateUnknown": true, "ignoreCurrentStateUnavailable": true, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 110, "y": 340, "wires": [["557a2b498f2cdeed"]]}, {"id": "557a2b498f2cdeed", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "充電處理器", "func": "// 獲取配置\nconst batteries = flow.get('batteries');\nif (!batteries) {\n    node.error('系統未初始化，請先執行系統初始化');\n    return null;\n}\n\n// 解析狀態變化\nconst newState = msg.payload;\nconst oldState = msg.data.old_state?.state;\nconst entityId = msg.data.entity_id;\n\n// 找到對應的電池\nlet batteryKey = null;\nlet battery = null;\n\nfor (const [key, config] of Object.entries(batteries)) {\n    if (config.entityId === entityId) {\n        batteryKey = key;\n        battery = config;\n        break;\n    }\n}\n\nif (!battery) {\n    node.error(`找不到實體 ${entityId} 的配置`);\n    return null;\n}\n\n// 準備輸出\nconst outputs = [null, null, null]; // [通知, 控制, debug]\n\n// 檢查開啟狀態 (off -> on)\nif (oldState === 'off' && newState === 'on') {\n    // 清除現有計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n    }\n    \n    // 記錄開始時間\n    const startTime = new Date();\n    flow.set(`${batteryKey}_start`, startTime.getTime());\n    \n    // 更新充電記錄\n    const records = flow.get('chargingRecords') || {};\n    if (!records[batteryKey]) records[batteryKey] = {};\n    records[batteryKey].lastCharge = startTime.toISOString();\n    records[batteryKey].count = (records[batteryKey].count || 0) + 1;\n    flow.set('chargingRecords', records);\n    \n    // 設定自動關閉計時器\n    const chargingMs = battery.chargingTime * 60 * 1000;\n    const newTimerId = setTimeout(() => {\n        // 發送關閉命令\n        const controlMsg = {\n            payload: {\n                domain: battery.domain,\n                service: 'turn_off',\n                target: {\n                    entity_id: entityId\n                }\n            },\n            batteryKey: batteryKey,\n            action: 'auto_stop'\n        };\n        \n        // 發送到控制輸出\n        node.send([null, controlMsg, {\n            payload: `自動關閉 ${battery.name}`,\n            batteryKey: batteryKey,\n            action: 'auto_stop',\n            controlMsg: controlMsg\n        }]);\n        \n        flow.set(`${batteryKey}_timer`, null);\n    }, chargingMs);\n    \n    flow.set(`${batteryKey}_timer`, newTimerId);\n    \n    // 準備通知訊息\n    const notifyMsg = {\n        payload: {\n            title: '🔋 充電開始',\n            message: `🔋 ${battery.name} 開始充電\\n⏱️ 預計時間：${battery.chargingTime}分鐘\\n🕐 開始時間：${startTime.toLocaleString('zh-TW')}`\n        },\n        batteryKey: batteryKey,\n        action: 'start'\n    };\n    \n    outputs[0] = notifyMsg;\n    outputs[2] = {\n        payload: `${battery.name} 開始充電`,\n        batteryKey: batteryKey,\n        chargingTime: battery.chargingTime,\n        timerId: newTimerId,\n        entityId: entityId,\n        domain: battery.domain\n    };\n}\n\n// 檢查關閉狀態 (on -> off)\nelse if (newState === 'off' && oldState === 'on') {\n    // 清除計時器\n    const timerId = flow.get(`${batteryKey}_timer`);\n    if (timerId) {\n        clearTimeout(timerId);\n        flow.set(`${batteryKey}_timer`, null);\n    }\n    \n    // 計算實際時間\n    const startTime = flow.get(`${batteryKey}_start`);\n    const actualTime = startTime ? Math.round((Date.now() - startTime) / 60000) : 0;\n    \n    // 準備完成通知\n    const notifyMsg = {\n        payload: {\n            title: '✅ 充電完成',\n            message: `✅ ${battery.name} 充電完成\\n⏱️ 充電時長：${actualTime}分鐘\\n🕐 完成時間：${new Date().toLocaleString('zh-TW')}`\n        },\n        batteryKey: batteryKey,\n        action: 'complete'\n    };\n    \n    outputs[0] = notifyMsg;\n    outputs[2] = {\n        payload: `${battery.name} 充電完成`,\n        batteryKey: batteryKey,\n        actualTime: actualTime,\n        entityId: entityId\n    };\n}\n\nreturn outputs;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 280, "wires": [["d320c63adb96c9b2"], ["57a17fab27364441"], ["f939fa43178e5d5d"]]}, {"id": "d320c63adb96c9b2", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "通知分發器", "func": "// 檢查是否有通知訊息\nif (!msg || !msg.payload) {\n    return [null, null, null];\n}\n\n// 動態靜音時間控制\nlet quietMode = flow.get('quietMode');\nif (quietMode === undefined) {\n    quietMode = false; // 預設關閉靜音（測試模式）\n    flow.set('quietMode', quietMode);\n}\n\nlet isQuiet = false;\nif (quietMode) {\n    // 靜音模式開啟時，檢查時間\n    const now = new Date();\n    const hour = now.getHours();\n    isQuiet = hour >= 22 || hour < 7;\n}\n// 如果靜音模式關閉，isQuiet 保持 false\n\nif (isQuiet && msg.action !== 'error') {\n    return [null, null, null];\n}\n\nconst title = msg.payload.title;\nconst message = msg.payload.message;\n\n// 創建三個通知訊息 - 修正版\nconst notify1 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nconst notify2 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nconst notify3 = {\n    payload: {\n        title: title,\n        message: message\n    }\n};\n\nreturn [notify1, notify2, notify3];", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 550, "y": 80, "wires": [["notify_helper_function"], ["89aee30dfac00f5b"], ["a8170b658dfeddbc"]]}, {"id": "notify_helper_function", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "NotifyHelper處理器", "func": "// 準備NotifyHelper通知數據 - 參考空調系統格式\nmsg.payload_notifyhelper = {\n    message: msg.payload.message,\n    title: msg.payload.title,\n    targets: ['person.ming']\n};\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 790, "y": 40, "wires": [["ec76955e19ebb9b1"]]}, {"id": "ec76955e19ebb9b1", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "NotifyHelper (ming)", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.notify_person", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload_notifyhelper.message, \"title\": payload_notifyhelper.title, \"targets\": payload_notifyhelper.targets}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": true, "domain": "notify", "service": "notify_person", "x": 1010, "y": 40, "wires": [["f2c6b9c1a5fd62bb"]]}, {"id": "89aee30dfac00f5b", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "Synology Chat", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.synology_chat_bot_3", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"title\": payload.title, \"message\": payload.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "notify", "service": "synology_chat_bot_3", "x": 780, "y": 100, "wires": [["d69d04aaad89af7c"]]}, {"id": "a8170b658dfeddbc", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "Telegram", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.telegram", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"title\": payload.title, \"message\": payload.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "notify", "service": "telegram", "x": 760, "y": 160, "wires": [["df4bde76cae2ba0d"]]}, {"id": "57a17fab27364441", "type": "api-call-service", "z": "7aa6bd9d6c51a86b", "name": "設備控制", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "{{payload.domain}}.{{payload.service}}", "floorId": [], "areaId": [], "deviceId": [], "entityId": ["{{payload.target.entity_id}}"], "labelId": [], "data": "{}", "dataType": "json", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": false, "domain": "", "service": "", "x": 580, "y": 280, "wires": [["9b577c831d8eaae5"]]}, {"id": "09dee24b595fdca5", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "0 9 * * *", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 110, "y": 440, "wires": [["135d77f387f44487"]]}, {"id": "18ac5c3dcf5bf3f1", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "手動維護檢查", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "maintenance_check", "payloadType": "str", "x": 110, "y": 480, "wires": [["135d77f387f44487"]]}, {"id": "135d77f387f44487", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "維護檢查邏輯", "func": "// 獲取配置\nconst batteries = flow.get('batteries');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteries) {\n    node.error('系統未初始化');\n    return null;\n}\n\nconst now = new Date();\nconst maintenanceNeeded = [];\n\n// 檢查每個電池的維護需求\nfor (const [key, battery] of Object.entries(batteries)) {\n    const record = records[key];\n    \n    if (!record || !record.lastCharge) {\n        // 從未充電，需要維護\n        maintenanceNeeded.push({\n            key: key,\n            battery: battery,\n            reason: '從未充電',\n            daysSince: '未知'\n        });\n        continue;\n    }\n    \n    const lastCharge = new Date(record.lastCharge);\n    const daysSince = Math.floor((now - lastCharge) / (1000 * 60 * 60 * 24));\n    \n    if (daysSince >= battery.maintenanceDays) {\n        maintenanceNeeded.push({\n            key: key,\n            battery: battery,\n            reason: `超過${battery.maintenanceDays}天未充電`,\n            daysSince: daysSince\n        });\n    }\n}\n\n// 準備輸出\nconst outputs = [null, null, null]; // [通知, 控制, debug]\n\nif (maintenanceNeeded.length > 0) {\n    // 準備維護通知\n    const batteryList = maintenanceNeeded.map(item => \n        `• ${item.battery.name} (${item.daysSince}天前)`\n    ).join('\\n');\n    \n    const notifyMsg = {\n        payload: {\n            title: '🔧 電池維護提醒',\n            message: `🔧 以下電池需要維護充電：\\n${batteryList}\\n\\n建議進行維護充電以保持電池性能。`\n        },\n        action: 'maintenance_alert',\n        maintenanceList: maintenanceNeeded\n    };\n    \n    outputs[0] = notifyMsg;\n    \n    // 自動開啟第一個需要維護的電池\n    const firstBattery = maintenanceNeeded[0];\n    const controlMsg = {\n        payload: {\n            domain: firstBattery.battery.domain,\n            service: 'turn_on',\n            target: {\n                entity_id: firstBattery.battery.entityId\n            }\n        },\n        batteryKey: firstBattery.key,\n        action: 'maintenance_start'\n    };\n    \n    outputs[1] = controlMsg;\n} else {\n    // 無需維護\n    const notifyMsg = {\n        payload: {\n            title: '✅ 電池狀態良好',\n            message: '✅ 所有電池狀態良好，無需維護充電。'\n        },\n        action: 'maintenance_ok'\n    };\n    \n    outputs[0] = notifyMsg;\n}\n\noutputs[2] = {\n    payload: `維護檢查完成`,\n    maintenanceNeeded: maintenanceNeeded,\n    totalBatteries: Object.keys(batteries).length,\n    checkTime: now.toISOString()\n};\n\nreturn outputs;", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 460, "wires": [["d320c63adb96c9b2"], ["57a17fab27364441"], ["b092e83ad5276d5c"]]}, {"id": "6ccde770b793e659", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "狀態報告", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "status_report", "payloadType": "str", "x": 100, "y": 540, "wires": [["7c10f14e00c3e5c2"]]}, {"id": "7c10f14e00c3e5c2", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "狀態報告邏輯", "func": "// 獲取配置和記錄\nconst batteries = flow.get('batteries');\nconst records = flow.get('chargingRecords') || {};\n\nif (!batteries) {\n    node.error('系統未初始化');\n    return null;\n}\n\nconst now = new Date();\nlet statusReport = '📊 電池充電系統狀態報告\\n\\n';\n\n// 系統資訊\nstatusReport += `🕐 報告時間：${now.toLocaleString('zh-TW')}\\n`;\nstatusReport += `🔋 管理電池數量：${Object.keys(batteries).length}\\n\\n`;\n\n// 各電池狀態\nfor (const [key, battery] of Object.entries(batteries)) {\n    const record = records[key] || {};\n    \n    statusReport += `🔋 ${battery.name}\\n`;\n    statusReport += `   📍 插座：${battery.entityId}\\n`;\n    statusReport += `   ⏱️ 充電時間：${battery.chargingTime}分鐘\\n`;\n    statusReport += `   🔧 維護週期：${battery.maintenanceDays}天\\n`;\n    \n    if (record.lastCharge) {\n        const lastCharge = new Date(record.lastCharge);\n        const daysSince = Math.floor((now - lastCharge) / (1000 * 60 * 60 * 24));\n        statusReport += `   📅 上次充電：${lastCharge.toLocaleString('zh-TW')} (${daysSince}天前)\\n`;\n        statusReport += `   📈 充電次數：${record.count || 0}次\\n`;\n        \n        // 維護狀態\n        if (daysSince >= battery.maintenanceDays) {\n            statusReport += `   ⚠️ 狀態：需要維護充電\\n`;\n        } else {\n            statusReport += `   ✅ 狀態：良好\\n`;\n        }\n    } else {\n        statusReport += `   📅 上次充電：從未充電\\n`;\n        statusReport += `   📈 充電次數：0次\\n`;\n        statusReport += `   ⚠️ 狀態：需要初次充電\\n`;\n    }\n    \n    statusReport += '\\n';\n}\n\n// 準備通知訊息\nconst notifyMsg = {\n    payload: {\n        title: '📊 系統狀態報告',\n        message: statusReport\n    },\n    action: 'status_report'\n};\n\nconst debugMsg = {\n    payload: '狀態報告已生成',\n    batteries: batteries,\n    records: records,\n    reportTime: now.toISOString()\n};\n\nreturn [notifyMsg, null, debugMsg];", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 540, "wires": [["d320c63adb96c9b2"], [], ["1a5a79ed498bfe9b"]]}, {"id": "f939fa43178e5d5d", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "充電Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 590, "y": 340, "wires": []}, {"id": "f2c6b9c1a5fd62bb", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "NotifyHelper Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1230, "y": 40, "wires": []}, {"id": "d69d04aaad89af7c", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "Synology Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1000, "y": 100, "wires": []}, {"id": "df4bde76cae2ba0d", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "Telegram Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1000, "y": 160, "wires": []}, {"id": "9b577c831d8eaae5", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "控制Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 770, "y": 280, "wires": []}, {"id": "b092e83ad5276d5c", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "維護Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 550, "y": 480, "wires": []}, {"id": "1a5a79ed498bfe9b", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "狀態Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 530, "y": 560, "wires": []}, {"id": "d210ccf10788c81c", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "測試通知", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"title\":\"🧪 測試通知 (修正版)\",\"message\":\"這是一個測試通知訊息，用於檢查通知服務是否正常運作。\\n\\n📱 NotifyHelper: 使用Function節點處理 → person.ming\\n💬 Synology Chat: 群組通知\\n📨 Telegram: 群組通知\\n\\n✅ 已修正JSONata語法錯誤\"}", "payloadType": "json", "x": 100, "y": 80, "wires": [["d320c63adb96c9b2"]]}, {"id": "bd428e4cf8125c71", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "測試設備控制", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"domain\":\"switch\",\"service\":\"turn_off\",\"target\":{\"entity_id\":\"switch.tp_link_power_strip_eb2f_cha_shang_4\"}}", "payloadType": "json", "x": 110, "y": 400, "wires": [["57a17fab27364441"]]}, {"id": "ee13514195b16430", "type": "inject", "z": "7aa6bd9d6c51a86b", "name": "切換靜音模式", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "toggle_quiet", "payloadType": "str", "x": 110, "y": 140, "wires": [["af5071c9556c6958"]]}, {"id": "af5071c9556c6958", "type": "function", "z": "7aa6bd9d6c51a86b", "name": "靜音模式控制", "func": "// 獲取當前靜音模式狀態\nlet quietMode = flow.get('quietMode');\nif (quietMode === undefined) {\n    quietMode = false; // 預設關閉靜音（測試模式）\n}\n\n// 切換靜音模式\nquietMode = !quietMode;\nflow.set('quietMode', quietMode);\n\n// 準備狀態訊息\nconst statusMsg = {\n    payload: {\n        title: quietMode ? '🔕 靜音模式已開啟' : '🔔 靜音模式已關閉',\n        message: quietMode ? \n            '🔕 靜音模式已開啟\\n⏰ 22:00-07:00 期間不會發送通知' : \n            '🔔 靜音模式已關閉\\n📱 所有時間都會發送通知（測試模式）'\n    },\n    action: 'quiet_mode_change'\n};\n\nconst debugMsg = {\n    payload: `靜音模式: ${quietMode ? '開啟' : '關閉'}`,\n    quietMode: quietMode,\n    timestamp: new Date().toISOString()\n};\n\nnode.status({\n    fill: quietMode ? \"yellow\" : \"green\",\n    shape: \"dot\",\n    text: quietMode ? \"靜音模式\" : \"正常模式\"\n});\n\nreturn [statusMsg, debugMsg];", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 140, "wires": [["d320c63adb96c9b2"], ["939ed0a84a81a358"]]}, {"id": "939ed0a84a81a358", "type": "debug", "z": "7aa6bd9d6c51a86b", "name": "靜音Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 550, "y": 160, "wires": []}, {"id": "125d83f7.1a33dc", "type": "server", "name": "Home Assistant", "addon": true}]