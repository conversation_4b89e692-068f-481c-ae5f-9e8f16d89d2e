# 🎉 空調設備通知系統 - 修正版使用指南

## ✅ 問題已修正！

### 🔧 主要修正內容
- ❌ **錯誤原因**：`api-call-service` 節點的 `data` 欄位包含了 `domain` 和 `service`
- ✅ **修正方案**：移除 `data` 中的 `domain` 和 `service`，這些參數應該在節點的其他欄位設定

### 📁 修正後的文件
**`aircon-notification-fixed.json`** - 完全修正的流程，可直接匯入使用

---

## 🚀 快速使用

### 1️⃣ 直接匯入
```
Node-RED → 選單 → 導入 → 選擇 aircon-notification-fixed.json
```

### 2️⃣ 測試功能
點擊以下測試按鈕驗證：
- **測試冷氣模式變更** - 模擬開啟冷氣和溫度設定
- **測試溫度調整** - 模擬溫度、風速、擺風變化  
- **測試除濕機開關** - 模擬除濕機開關

### 3️⃣ 檢查通知
- **NotifyHelper** - 檢查手機 Home Assistant App 通知
- **Synology Chat** - 檢查群暉聊天室
- **Telegram** - 檢查 Telegram 訊息

### 4️⃣ 部署使用
點擊「部署」按鈕啟動監控

---

## 🔔 NotifyHelper 正確配置

### ✅ 修正後的節點配置
```json
{
    "action": "notify.notify_person",
    "domain": "notify",
    "service": "notify_person",
    "data": {
        "message": "訊息內容",
        "title": "通知標題",
        "targets": ["person.ming"]
    }
}
```

### 🔑 關鍵要點
- **Service**: 使用 `notify_person`（不是 `notify`）
- **Data**: 只包含 `message`、`title`、`targets`
- **Targets**: 必須是陣列格式 `["person.ming"]`
- **Domain**: 在節點配置中設定，不在 data 中

---

## 📱 通知訊息範例

### 🔄 模式變更通知
```
2025/07/07 下午03:44:31
🌪️ 次臥冷氣
位置：次臥室
模式：送風模式
目前溫度：26°C
```

### 🌡️ 溫度調整通知
```
2025/07/07 下午04:15:20
❄️ 主臥冷氣
位置：主臥室
設定溫度：22°C
風速：高風速
擺風：垂直擺風
目前溫度：25°C
```

### 🟢 設備開關通知
```
2025/07/07 下午05:30:45
🟢 吊隱除濕機
位置：客廳
模式：開啟
```

---

## 🎯 監控功能

### 📊 完整狀態追蹤
- ✅ **開關狀態** - 開啟/關閉
- ✅ **運行模式** - 冷氣/暖氣/除濕/送風/自動
- ✅ **溫度設定** - 監控溫度調整（16°C-32°C）
- ✅ **風速變化** - 自動/靜音/低/中/高風速
- ✅ **擺風模式** - 關閉/垂直/水平/全方向擺風
- ✅ **當前溫度** - 顯示室內實際溫度

### 🛡️ 智能防護
- ⏰ **60秒冷卻期** - 避免重複通知
- 🧠 **智能比較** - 只在真正變化時通知
- 🎯 **精確追蹤** - 監控所有重要屬性變化

---

## 🔧 自定義設定

### 修改 person 實體
如果您的 person 實體不是 `person.ming`：

1. 雙擊「NotifyHelper通知」節點
2. 在 data 欄位中找到：
   ```json
   "targets": payload_notifyhelper.targets
   ```
3. 雙擊「建立通知訊息」節點
4. 修改：
   ```javascript
   targets: ["person.你的實體ID"]
   ```
5. 部署流程

### 調整冷卻時間
在「增強狀態處理器」中修改：
```javascript
// 檢查是否在冷卻期內（60秒）
if (lastNotification.time && (currentTime - lastNotification.time) < 60000) {
```
將 `60000` 改為其他值（毫秒）

### 添加新的監控屬性
在狀態處理器中添加：
```javascript
// 檢查新屬性變化
if (newAttrs.your_attribute !== oldAttrs.your_attribute) {
    attributeChanges.push({
        type: 'your_attribute',
        old: oldAttrs.your_attribute,
        new: newAttrs.your_attribute
    });
}
```

---

## 🛠️ 故障排除

### ❌ 仍然收到錯誤？
**檢查項目**：
1. 確認已使用修正版流程 `aircon-notification-fixed.json`
2. 檢查 NotifyHelper 是否已正確安裝
3. 確認 `person.ming` 實體存在
4. 查看除錯面板的詳細錯誤訊息

### ❌ NotifyHelper 沒收到通知？
**解決方案**：
1. 確認 NotifyHelper 整合已安裝並啟用
2. 檢查開發者工具中是否有 `notify.notify_person` 服務
3. 確認 person 實體有關聯的行動裝置
4. 測試手動調用服務：
   ```yaml
   service: notify.notify_person
   data:
     message: "測試訊息"
     title: "測試標題"
     targets:
       - person.ming
   ```

### ❌ 其他通知服務正常，只有 NotifyHelper 有問題？
**替代方案**：
```json
{
    "action": "notify.mobile_app_你的手機名稱",
    "data": {
        "message": "訊息內容",
        "title": "通知標題"
    }
}
```

---

## 📊 系統狀態

### ✅ 正常運作指標
- 除錯面板顯示狀態變更日誌
- 三個通知服務都有輸出
- 沒有錯誤訊息在錯誤日誌中

### 🔍 監控項目
- **5個設備** 全部正常監控
- **所有狀態變化** 都會觸發通知
- **防重複機制** 正常運作
- **三種通知方式** 同時發送

---

## 🎉 完成！

現在您的空調設備通知系統已經完全修正，可以：

- 🔍 **全面監控** - 所有設備的所有狀態變化
- 📱 **多平台通知** - NotifyHelper、Synology Chat、Telegram
- 🛡️ **智能防護** - 防重複、冷卻期、錯誤處理
- 🎯 **精確追蹤** - 溫度、模式、風速、擺風等

享受您的智能家居全方位監控系統！🏠✨
